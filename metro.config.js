// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add polyfills for Supabase to work with React Native
config.resolver.extraNodeModules = {
  ...config.resolver.extraNodeModules,
  // Common Node.js modules used by Supabase
  crypto: require.resolve('crypto-browserify'),
  stream: require.resolve('stream-browserify'),
  url: require.resolve('url'),
  https: require.resolve('https-browserify'),
  http: require.resolve('@tradle/react-native-http'),
  os: require.resolve('os-browserify'),
  events: require.resolve('events'),
  buffer: require.resolve('buffer'),
};

module.exports = config;

