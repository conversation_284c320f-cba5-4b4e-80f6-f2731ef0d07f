// Simple email server for sending verification codes
// Run this with: node email-server.js

const express = require('express');
const nodemailer = require('nodemailer');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Gmail SMTP configuration
const transporter = nodemailer.createTransport({
  host: 'smtp.gmail.com',  
  port: 587,
  secure: false, // true for 465, false for other ports
  auth: {
    user: '<EMAIL>',
    pass: 'eufa iqpz rupl mway' // Your Gmail app password
  },
  tls: {
    rejectUnauthorized: false
  }
});

// Test the connection
transporter.verify((error, success) => {
  if (error) {
    console.log('❌ Email server connection failed:', error);
  } else {
    console.log('✅ Email server ready to send emails');
  }
});

// Send verification email endpoint
app.post('/send-verification', async (req, res) => {
  console.log('📧 Received email request:', req.body);

  try {
    const { email, verificationCode } = req.body;

    if (!email || !verificationCode) {
      console.log('❌ Missing email or verification code');
      return res.status(400).json({
        success: false,
        error: 'Email and verification code are required'
      });
    }

    console.log('📧 Sending verification email to:', email, 'with code:', verificationCode);

    const mailOptions = {
      from: '"MentalEase" <<EMAIL>>',
      to: email,
      subject: 'MentalEase - Email Verification Code',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #6B9142; margin: 0;">MentalEase</h1>
            <p style="color: #666; margin: 5px 0;">Your Mental Health Companion</p>
          </div>

          <h2 style="color: #333;">Welcome to MentalEase!</h2>
          <p style="color: #555; line-height: 1.6;">
            Thank you for signing up for MentalEase. To complete your registration and secure your account,
            please verify your email address using the code below.
          </p>

          <div style="background: linear-gradient(135deg, #6B9142, #8BC34A); padding: 25px; border-radius: 12px; text-align: center; margin: 25px 0;">
            <p style="color: white; margin: 0 0 10px 0; font-size: 16px;">Your Verification Code:</p>
            <h1 style="color: white; font-size: 36px; letter-spacing: 6px; margin: 0; font-weight: bold;">${verificationCode}</h1>
          </div>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #333; margin: 0 0 10px 0;">How to verify:</h3>
            <ol style="color: #555; margin: 0; padding-left: 20px;">
              <li>Open the MentalEase app</li>
              <li>Enter the 6-digit code above</li>
              <li>Tap "Verify Email"</li>
            </ol>
          </div>

          <p style="color: #666; font-size: 14px; line-height: 1.5;">
            <strong>Security Note:</strong> This code will expire in 10 minutes for your security.
            If you didn't sign up for MentalEase, please ignore this email.
          </p>

          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">

          <div style="text-align: center;">
            <p style="color: #999; font-size: 12px; margin: 0;">
              © 2024 MentalEase. All rights reserved.
            </p>
            <p style="color: #999; font-size: 12px; margin: 5px 0 0 0;">
              This is an automated message, please do not reply.
            </p>
          </div>
        </div>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('✅ Email sent successfully to:', email);
    console.log('Message ID:', info.messageId);

    res.json({
      success: true,
      message: 'Verification email sent successfully',
      messageId: info.messageId
    });

  } catch (error) {
    console.error('❌ Error sending email:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send email: ' + error.message
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'Email server is running', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(`📧 Email server running on http://localhost:${PORT}`);
  console.log('📧 Ready to send verification emails via Gmail SMTP');
});
