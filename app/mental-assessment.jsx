import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  FlatList,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BottomNavigation from './components/BottomNavigation';

const MentalAssessment = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [assessmentStatus, setAssessmentStatus] = useState({});
  
  // Base assessment data
  const assessments = [
    {
      id: 'dass',
      title: 'DASS (Depression Anxiety Stress Scales)',
      description: 'A comprehensive assessment that measures three related negative emotional states of depression, anxiety, and stress. This test helps identify the severity of these core symptoms.',
      questions: 21,
      icon: 'D',
      color: '#6B9142'
    },
    {
      id: 'pss',
      title: 'PSS (Perceived Stress Scale)',
      description: 'A widely used psychological instrument that measures your perception of stress. It assesses how unpredictable, uncontrollable, and overloaded you find your life.',
      questions: 10,
      icon: 'P',
      color: '#6B9142'
    }
  ];

  // Check for saved results when component mounts
  useEffect(() => {
    checkAssessmentStatus();
  }, []);

  // Function to check assessment status
  const checkAssessmentStatus = async () => {
    setIsLoading(true);
    try {
      const status = {};
      
      // Check each assessment
      for (const assessment of assessments) {
        const savedResults = await AsyncStorage.getItem(`${assessment.id}_saved_results`);
        const timestamp = await AsyncStorage.getItem(`${assessment.id}_result_timestamp`);
        
        status[assessment.id] = {
          hasResults: savedResults !== null,
          timestamp: timestamp ? new Date(timestamp) : null
        };
        
        console.log(`${assessment.id} status:`, status[assessment.id]);
      }
      
      setAssessmentStatus(status);
    } catch (error) {
      console.error('Error checking assessment status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAssessmentSelect = (assessment) => {
    router.push({
      pathname: '/assessment-questions',
      params: { id: assessment.id }
    });
  };

  const handleViewResults = async (assessmentId) => {
  try {
    console.log(`Attempting to view results for ${assessmentId}`);
    
    // Always get the latest results directly from AsyncStorage
    const savedResults = await AsyncStorage.getItem(`${assessmentId}_saved_results`);
    const savedTimestamp = await AsyncStorage.getItem(`${assessmentId}_result_timestamp`);
    
    console.log(`${assessmentId} saved results found:`, savedResults ? 'Yes' : 'No');
    console.log(`${assessmentId} timestamp:`, savedTimestamp);
    
    if (savedResults) {
      // Add a unique timestamp parameter to force fresh data loading
      const timestamp = Date.now();
      
      router.push({
        pathname: '/assessment-results',
        params: { 
          id: assessmentId,
          results: savedResults,
          refresh: timestamp // Force the results screen to treat this as new data
        }
      });
    } else {
      Alert.alert(
        "No Results Found",
        "You haven't completed this assessment yet. Would you like to take it now?",
        [
          {
            text: "Yes",
            onPress: () => handleAssessmentSelect({id: assessmentId})
          },
          {
            text: "No",
            style: "cancel"
          }
        ]
      );
    }
  } catch (error) {
    console.error('Error viewing results:', error);
    Alert.alert("Error", "Could not load assessment results.");
  }
};

  const renderAssessmentItem = ({ item }) => {
    const hasResults = assessmentStatus[item.id]?.hasResults || false;
    
    return (
      <TouchableOpacity
        style={styles.assessmentCard}
        activeOpacity={0.9}
        onPress={() => handleAssessmentSelect(item)}
      >
        <View style={[styles.assessmentIcon, { backgroundColor: item.color }]}>
          <Text style={styles.assessmentIconText}>{item.icon}</Text>
        </View>
        <View style={styles.assessmentInfo}>
          <Text style={styles.assessmentTitle}>{item.title}</Text>
          <Text style={styles.assessmentDescription}>{item.description}</Text>
          
          <Text style={styles.assessmentQuestions}>{item.questions} questions</Text>
          
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.takeTestButton}
              onPress={() => handleAssessmentSelect(item)}
            >
              <Text style={styles.takeTestButtonText}>Take Test</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.viewResultsButton}
              onPress={() => handleViewResults(item.id)}
            >
              <Text style={styles.viewResultsButtonText}>View Results</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Mental Health Assessment</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6B9142" />
          <Text style={styles.loadingText}>Loading assessments...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Mental Health Assessment</Text>
      </View>
      <Text style={styles.subtitle}>
        Please select which assessment you would like to take today
      </Text>
      <FlatList
        data={assessments}
        renderItem={renderAssessmentItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.assessmentList}
      />
      <BottomNavigation />
    </SafeAreaView>
  );
};

export default MentalAssessment;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    color: '#7F8C8D',
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 15,
    paddingBottom: 15,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    color: '#7BA05B',
    fontSize: 17,
    fontWeight: '600',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#2C3E50',
    fontSize: 20,
    fontWeight: 'bold',
    marginRight: 40,
    letterSpacing: 0.5,
  },
  subtitle: {
    fontSize: 17,
    color: '#5D6D7E',
    textAlign: 'center',
    marginVertical: 20,
    paddingHorizontal: 24,
    lineHeight: 24,
  },
  assessmentList: {
    padding: 24,
    paddingBottom: 100, // Space for bottom navigation
  },
  assessmentCard: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    padding: 22,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  assessmentIcon: {
    width: 52,
    height: 52,
    borderRadius: 26,
    backgroundColor: '#7BA05B',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 20,
    shadowColor: '#7BA05B',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 4,
  },
  assessmentIconText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  assessmentInfo: {
    flex: 1,
  },
  assessmentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 8,
    letterSpacing: 0.3,
  },
  assessmentDescription: {
    fontSize: 15,
    color: '#5D6D7E',
    marginBottom: 12,
    lineHeight: 22,
  },
  assessmentQuestions: {
    fontSize: 13,
    color: '#95A5A6',
    marginBottom: 14,
    marginTop: 4,
    fontWeight: '500',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  takeTestButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 24,
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
    shadowColor: '#7BA05B',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 4,
  },
  takeTestButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
    letterSpacing: 0.3,
  },
  viewResultsButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 24,
    flex: 1,
    marginLeft: 10,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#7BA05B',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  viewResultsButtonText: {
    color: '#7BA05B',
    fontSize: 14,
    fontWeight: 'bold',
    letterSpacing: 0.3,
  },
});