import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  TextInput,
  Modal,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter } from 'expo-router';
import { useAppointment } from './context/AppointmentContext';
import CustomStatusBar from './components/CustomStatusBar';

const ScheduleAppointment = () => {
  const router = useRouter();
  const {
    psychometricians,
    bookAppointment,
    isTimeSlotAvailable,
    getDates,
    getTimeSlots,
    formatDate,
    formatDateString,
    appointments,
    allAppointments,
    fetchAppointments
  } = useAppointment();

  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [selectedPsychometrician, setSelectedPsychometrician] = useState(null);
  const [notes, setNotes] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [availableTimeSlots, setAvailableTimeSlots] = useState([]);
  const [isBooking, setIsBooking] = useState(false);
  const [bookingError, setBookingError] = useState(null);

  // Update available time slots when psychometrician or date changes
  useEffect(() => {
    if (selectedPsychometrician && selectedDate) {
      const slots = getTimeSlots(selectedPsychometrician.id, selectedDate);
      setAvailableTimeSlots(slots);
      setSelectedTimeSlot(null); // Reset selected time slot
    } else {
      setAvailableTimeSlots([]);
    }
  }, [selectedPsychometrician, selectedDate, getTimeSlots]);

  // Refresh time slots when appointments change (for real-time updates)
  useEffect(() => {
    if (selectedPsychometrician && selectedDate) {
      const slots = getTimeSlots(selectedPsychometrician.id, selectedDate);
      setAvailableTimeSlots(slots);

      // Check if currently selected time slot is still available
      if (selectedTimeSlot) {
        const isStillAvailable = isTimeSlotAvailable(
          selectedDate,
          selectedTimeSlot,
          selectedPsychometrician.id
        );
        if (!isStillAvailable) {
          setSelectedTimeSlot(null); // Clear selection if no longer available
        }
      }
    }
  }, [appointments, allAppointments]); // Also listen to allAppointments changes

  const handleBookAppointment = () => {
    if (selectedDate && selectedTimeSlot && selectedPsychometrician) {
      setShowConfirmModal(true);
    }
  };

  const confirmAppointment = () => {
    // After confirming details, show payment modal
    setShowConfirmModal(false);
    setShowPaymentModal(true);
  };

  const processPayment = async () => {
    // Process payment and book the appointment
    setIsBooking(true);
    setBookingError(null);

    try {
      // Double-check availability before booking
      const isStillAvailable = isTimeSlotAvailable(
        selectedDate,
        selectedTimeSlot,
        selectedPsychometrician.id
      );

      if (!isStillAvailable) {
        throw new Error('This time slot is no longer available. Please select a different time.');
      }

      await bookAppointment(
        selectedPsychometrician.id,
        selectedDate,
        selectedTimeSlot,
        notes
      );

      setShowPaymentModal(false);
      setShowSuccessModal(true);

      // Reset form
      setSelectedDate(null);
      setSelectedTimeSlot(null);
      setSelectedPsychometrician(null);
      setNotes('');
      setSelectedPaymentMethod(null);

    } catch (error) {
      console.error('Error booking appointment:', error);
      setBookingError(error.message || 'Failed to book appointment. Please try again.');
    } finally {
      setIsBooking(false);
    }
  };

  const closeSuccessModal = () => {
    setShowSuccessModal(false);
    router.replace('/appointments');
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={styles.container}>
          <CustomStatusBar backgroundColor="transparent" barStyle="dark-content" />

          <View style={styles.header}>
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Schedule Appointment</Text>
          </View>

          <ScrollView contentContainerStyle={styles.content}>
            <Text style={styles.sectionTitle}>Select a Psychometrician</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.therapistsContainer}
            >
              {psychometricians.map((psychometrician) => (
                <TouchableOpacity
                  key={psychometrician.id}
                  style={[
                    styles.therapistCard,
                    selectedPsychometrician?.id === psychometrician.id && styles.selectedTherapistCard,
                    !psychometrician.available && styles.unavailableTherapistCard
                  ]}
                  onPress={() => psychometrician.available !== false && setSelectedPsychometrician(psychometrician)}
                  disabled={psychometrician.available === false}
                >
                  <Text style={styles.therapistImage}>{psychometrician.image || '👨‍⚕️'}</Text>
                  <Text style={styles.therapistName}>{psychometrician.name}</Text>
                  <Text style={styles.therapistSpecialty}>{psychometrician.specialty || 'Psychometrician'}</Text>
                  <View style={styles.therapistInfoRow}>
                    <Text style={styles.therapistExperience}>{psychometrician.experience || '5+ years'}</Text>
                    <Text style={styles.therapistRating}>⭐ {psychometrician.rating || '4.8'}</Text>
                  </View>
                  {psychometrician.available === false && (
                    <View style={styles.unavailableOverlay}>
                      <Text style={styles.unavailableText}>Unavailable</Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            <Text style={styles.sectionTitle}>Select a Date</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.datesContainer}
            >
              {getDates().map((date, index) => {
                const formattedDate = formatDate(date);
                const dateString = formatDateString(date);
                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.dateCard,
                      selectedDate === dateString && styles.selectedDateCard
                    ]}
                    onPress={() => setSelectedDate(dateString)}
                  >
                    <Text style={styles.dateDay}>{formattedDate.day}</Text>
                    <Text style={styles.dateNumber}>{formattedDate.date}</Text>
                    <Text style={styles.dateMonth}>{formattedDate.month}</Text>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>

            <Text style={styles.sectionTitle}>Select a Time</Text>
            <View style={styles.timeContainer}>
              {availableTimeSlots.map((timeSlot, index) => {
                // Safety check to prevent undefined timeSlot errors
                if (!timeSlot || !timeSlot.start_time || !timeSlot.end_time) {
                  return null;
                }

                const available = selectedDate && selectedPsychometrician ?
                  isTimeSlotAvailable(selectedDate, timeSlot, selectedPsychometrician.id) : true;
                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.timeCard,
                      selectedTimeSlot?.start_time === timeSlot.start_time && styles.selectedTimeCard,
                      !available && styles.unavailableTimeCard
                    ]}
                    onPress={() => available && setSelectedTimeSlot(timeSlot)}
                    disabled={!available}
                  >
                    <Text
                      style={[
                        styles.timeText,
                        selectedTimeSlot?.start_time === timeSlot.start_time && styles.selectedTimeText,
                        !available && styles.unavailableTimeText
                      ]}
                    >
                      {timeSlot.display}
                    </Text>
                    {!available && (
                      <Text style={styles.bookedText}>Booked</Text>
                    )}
                  </TouchableOpacity>
                );
              })}
              {availableTimeSlots.length === 0 && selectedPsychometrician && selectedDate && (
                <Text style={styles.noTimeSlotsText}>
                  No available time slots for this psychometrician on the selected date.
                </Text>
              )}
            </View>

            <Text style={styles.sectionTitle}>Additional Notes</Text>
            <TextInput
              style={styles.notesInput}
              placeholder="Add any notes for your psychometrician..."
              value={notes}
              onChangeText={setNotes}
              multiline
              textAlignVertical="top"
            />

            <TouchableOpacity
              style={[
                styles.bookButton,
                (!selectedDate || !selectedTimeSlot || !selectedPsychometrician) && styles.disabledButton
              ]}
              onPress={handleBookAppointment}
              disabled={!selectedDate || !selectedTimeSlot || !selectedPsychometrician}
            >
              <Text style={styles.bookButtonText}>Book Appointment</Text>
            </TouchableOpacity>
          </ScrollView>

          {/* Confirmation Modal */}
          <Modal
            visible={showConfirmModal}
            transparent={true}
            animationType="none"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Confirm Appointment</Text>

                {selectedPsychometrician && (
                  <View style={styles.confirmTherapist}>
                    <Text style={styles.confirmTherapistImage}>{selectedPsychometrician.image || '👨‍⚕️'}</Text>
                    <View style={styles.confirmTherapistInfo}>
                      <Text style={styles.confirmTherapistName}>{selectedPsychometrician.name}</Text>
                      <Text style={styles.confirmTherapistSpecialty}>{selectedPsychometrician.specialty || 'Psychometrician'}</Text>
                    </View>
                  </View>
                )}

                {selectedDate && selectedTimeSlot && (
                  <View style={styles.confirmDateTime}>
                    <Text style={styles.confirmDateTimeText}>
                      {new Date(selectedDate).toLocaleDateString('en-US', {
                        weekday: 'short',
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </Text>
                    <Text style={styles.confirmDateTimeText}>at {selectedTimeSlot.display}</Text>
                  </View>
                )}

                <View style={styles.confirmButtons}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowConfirmModal(false)}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.confirmButton}
                    onPress={confirmAppointment}
                  >
                    <Text style={styles.confirmButtonText}>Confirm</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>

          {/* Payment Modal */}
          <Modal
            visible={showPaymentModal}
            transparent={true}
            animationType="none"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Payment Method</Text>

                <Text style={styles.paymentInfoText}>
                  Please select a payment method for your appointment:
                </Text>

                <View style={styles.paymentOptionsContainer}>
                  <TouchableOpacity
                    style={[
                      styles.paymentOption,
                      selectedPaymentMethod === 'credit' && styles.selectedPaymentOption
                    ]}
                    onPress={() => setSelectedPaymentMethod('credit')}
                  >
                    <Text style={styles.paymentOptionIcon}>💳</Text>
                    <View style={styles.paymentOptionInfo}>
                      <Text style={styles.paymentOptionTitle}>Credit Card</Text>
                      <Text style={styles.paymentOptionDesc}>Visa, Mastercard, Amex</Text>
                    </View>
                    {selectedPaymentMethod === 'credit' && (
                      <View style={styles.paymentSelectedIndicator} />
                    )}
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.paymentOption,
                      selectedPaymentMethod === 'paypal' && styles.selectedPaymentOption
                    ]}
                    onPress={() => setSelectedPaymentMethod('paypal')}
                  >
                    <Text style={styles.paymentOptionIcon}>🅿️</Text>
                    <View style={styles.paymentOptionInfo}>
                      <Text style={styles.paymentOptionTitle}>PayPal</Text>
                      <Text style={styles.paymentOptionDesc}>Pay with your PayPal account</Text>
                    </View>
                    {selectedPaymentMethod === 'paypal' && (
                      <View style={styles.paymentSelectedIndicator} />
                    )}
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.paymentOption,
                      selectedPaymentMethod === 'insurance' && styles.selectedPaymentOption
                    ]}
                    onPress={() => setSelectedPaymentMethod('insurance')}
                  >
                    <Text style={styles.paymentOptionIcon}>🏥</Text>
                    <View style={styles.paymentOptionInfo}>
                      <Text style={styles.paymentOptionTitle}>Insurance</Text>
                      <Text style={styles.paymentOptionDesc}>Use your health insurance</Text>
                    </View>
                    {selectedPaymentMethod === 'insurance' && (
                      <View style={styles.paymentSelectedIndicator} />
                    )}
                  </TouchableOpacity>
                </View>

                <View style={styles.paymentSummary}>
                  <Text style={styles.paymentSummaryTitle}>Appointment Summary</Text>
                  <View style={styles.paymentSummaryRow}>
                    <Text style={styles.paymentSummaryLabel}>Consultation Fee:</Text>
                    <Text style={styles.paymentSummaryValue}>$120.00</Text>
                  </View>
                  <View style={styles.paymentSummaryRow}>
                    <Text style={styles.paymentSummaryLabel}>Platform Fee:</Text>
                    <Text style={styles.paymentSummaryValue}>$5.00</Text>
                  </View>
                  <View style={styles.paymentSummaryDivider} />
                  <View style={styles.paymentSummaryRow}>
                    <Text style={styles.paymentSummaryTotal}>Total:</Text>
                    <Text style={styles.paymentSummaryTotalValue}>$125.00</Text>
                  </View>
                </View>

                {bookingError && (
                  <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>{bookingError}</Text>
                  </View>
                )}

                <View style={styles.confirmButtons}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => {
                      setShowPaymentModal(false);
                      setBookingError(null);
                    }}
                    disabled={isBooking}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.confirmButton,
                      (!selectedPaymentMethod || isBooking) && styles.disabledButton
                    ]}
                    onPress={processPayment}
                    disabled={!selectedPaymentMethod || isBooking}
                  >
                    <Text style={styles.confirmButtonText}>
                      {isBooking ? 'Processing...' : 'Pay Now'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>

          {/* Success Modal */}
          <Modal
            visible={showSuccessModal}
            transparent={true}
            animationType="none"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.successIcon}>✅</Text>
                <Text style={styles.successTitle}>Appointment Booked!</Text>
                <Text style={styles.successMessage}>
                  Your appointment has been successfully scheduled and payment processed. You will receive a confirmation email shortly.
                </Text>

                <TouchableOpacity
                  style={styles.doneButton}
                  onPress={closeSuccessModal}
                >
                  <Text style={styles.doneButtonText}>Done</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 10 : 10,
    paddingBottom: 10,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: '#6B9142',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginLeft: 10,
  },
  content: {
    padding: 16,
    paddingBottom: 40,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  therapistsContainer: {
    paddingBottom: 10,
  },
  therapistCard: {
    width: 160,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedTherapistCard: {
    borderColor: '#6B9142',
    borderWidth: 2,
  },
  unavailableTherapistCard: {
    opacity: 0.6,
  },
  therapistImage: {
    fontSize: 40,
    marginBottom: 10,
    alignSelf: 'center',
  },
  therapistName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  therapistSpecialty: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 8,
  },
  therapistInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  therapistExperience: {
    fontSize: 11,
    color: '#888888',
  },
  therapistRating: {
    fontSize: 11,
    color: '#FF9500',
    fontWeight: 'bold',
  },
  unavailableOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unavailableText: {
    color: '#FF3B30',
    fontWeight: 'bold',
    fontSize: 14,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 10,
  },
  datesContainer: {
    paddingBottom: 10,
  },
  dateCard: {
    width: 70,
    height: 90,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedDateCard: {
    backgroundColor: '#6B9142',
  },
  dateDay: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  dateNumber: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 2,
  },
  dateMonth: {
    fontSize: 14,
    color: '#666666',
  },
  timeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  timeCard: {
    width: '30%',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 12,
    marginBottom: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedTimeCard: {
    backgroundColor: '#6B9142',
  },
  unavailableTimeCard: {
    backgroundColor: '#F5F5F5',
  },
  timeText: {
    fontSize: 14,
    color: '#333333',
  },
  selectedTimeText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  unavailableTimeText: {
    color: '#999999',
  },
  bookedText: {
    fontSize: 10,
    color: '#FF3B30',
    marginTop: 4,
  },
  noTimeSlotsText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 20,
  },
  notesInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    height: 120,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  bookButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: '#6B9142',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
    shadowColor: '#999999',
  },
  bookButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 340,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 20,
  },
  confirmTherapist: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  confirmTherapistImage: {
    fontSize: 40,
    marginRight: 15,
  },
  confirmTherapistInfo: {
    flex: 1,
  },
  confirmTherapistName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  confirmTherapistSpecialty: {
    fontSize: 14,
    color: '#666666',
  },
  confirmDateTime: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    marginBottom: 20,
    alignItems: 'center',
  },
  confirmDateTimeText: {
    fontSize: 16,
    color: '#333333',
    marginBottom: 4,
  },
  confirmButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginLeft: 10,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  successIcon: {
    fontSize: 50,
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
  },
  successMessage: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  doneButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 40,
    alignItems: 'center',
  },
  doneButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  // Payment Modal Styles
  paymentInfoText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
    textAlign: 'center',
  },
  paymentOptionsContainer: {
    width: '100%',
    marginBottom: 20,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F9EE',
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  selectedPaymentOption: {
    borderColor: '#6B9142',
    backgroundColor: '#F5F9EE',
  },
  paymentOptionIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  paymentOptionInfo: {
    flex: 1,
  },
  paymentOptionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 2,
  },
  paymentOptionDesc: {
    fontSize: 12,
    color: '#666666',
  },
  paymentSelectedIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#6B9142',
  },
  paymentSummary: {
    width: '100%',
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  paymentSummaryTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
  },
  paymentSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  paymentSummaryLabel: {
    fontSize: 13,
    color: '#666666',
  },
  paymentSummaryValue: {
    fontSize: 13,
    color: '#333333',
    fontWeight: '500',
  },
  paymentSummaryDivider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 8,
  },
  paymentSummaryTotal: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
  },
  paymentSummaryTotalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  errorContainer: {
    backgroundColor: '#FFE6E6',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    width: '100%',
  },
  errorText: {
    color: '#D32F2F',
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default ScheduleAppointment;
