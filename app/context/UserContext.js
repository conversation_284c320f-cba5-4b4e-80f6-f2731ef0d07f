import { createContext, useState, useContext } from 'react';

// Create the context
const UserContext = createContext();

export default UserContext;

// Create a provider component
export const UserProvider = ({ children }) => {
  const [userData, setUserData] = useState({
    // Basic info
    firstName: "",
    middleName: "",
    lastName: "",
    email: "<EMAIL>",
    controlNumber: "MH-2023-45678",
    phone: "",
    profileImage: null, // Placeholder for profile image

    // Additional personal info
    age: 0,
    gender: "",
    civilStatus: "",
    birthdate: "",
    birthplace: "",
    religion: "",
    address: "",

    // Authentication and profile status
    emailVerified: false,
    hasCompletedProfile: false, // Flag to track if user has completed their profile

    // App settings
    notifications: true,
    dataSharing: false,
    twoFactorAuth: true,
    darkMode: false,
    language: "English",
    hidePersonalInfo: false
  });

  // Function to update user data with optional callback
  const updateUserData = (newData, callback) => {
    setUserData(prevData => {
      const updatedData = {
        ...prevData,
        ...newData
      };

      // If a callback was provided, call it with the updated data
      if (callback) {
        setTimeout(() => callback(updatedData), 0);
      }

      return updatedData;
    });
  };

  // Toggle hide personal info
  const toggleHidePersonalInfo = () => {
    setUserData(prevData => ({
      ...prevData,
      hidePersonalInfo: !prevData.hidePersonalInfo
    }));
  };

  return (
    <UserContext.Provider value={{ userData, updateUserData, toggleHidePersonalInfo }}>
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use the context
export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
