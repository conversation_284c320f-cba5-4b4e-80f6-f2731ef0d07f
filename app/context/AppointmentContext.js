// AppointmentContext.js - Context for appointment data
import { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useUser } from './UserContext';

// Create the context
const AppointmentContext = createContext();

// Helper functions
const getDates = () => {
  const dates = [];
  const today = new Date();

  for (let i = 0; i < 14; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    dates.push(date);
  }

  return dates;
};

const formatDate = (date) => {
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  return {
    day: days[date.getDay()],
    date: date.getDate(),
    month: months[date.getMonth()],
    year: date.getFullYear(),
    fullDate: `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`
  };
};

const formatDateString = (date) => {
  // Use local date formatting to avoid timezone issues
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Create a provider component
export const AppointmentProvider = ({ children }) => {
  const { userData } = useUser(); // Get user data from UserContext
  const [appointments, setAppointments] = useState([]);
  const [allAppointments, setAllAppointments] = useState([]); // All appointments for availability checking
  const [upcomingAppointments, setUpcomingAppointments] = useState([]);
  const [pastAppointments, setPastAppointments] = useState([]);
  const [psychometricians, setPsychometricians] = useState([]);
  const [schedules, setSchedules] = useState([]);

  // Fetch psychometricians from database
  const fetchPsychometricians = async () => {
    try {
      const { data, error } = await supabase
        .from('psychometricians')
        .select('*')
        .execute();

      if (error) throw error;

      setPsychometricians(data || []);
    } catch (error) {
      console.error('Error fetching psychometricians:', error);
      // Fallback to mock data if database fetch fails
      setPsychometricians([
        {
          id: 1,
          name: 'Dr. Sarah Johnson',
          specialty: 'Anxiety & Depression',
          experience: '10 years',
          rating: 4.9,
          image: '👩‍⚕️',
          available: true
        },
        {
          id: 2,
          name: 'Dr. Michael Chen',
          specialty: 'Stress Management',
          experience: '8 years',
          rating: 4.7,
          image: '👨‍⚕️',
          available: true
        }
      ]);
    }
  };

  // Fetch schedules from database
  const fetchSchedules = async () => {
    try {
      const { data, error } = await supabase
        .from('schedules')
        .select('*')
        .execute();

      if (error) throw error;

      setSchedules(data || []);
    } catch (error) {
      console.error('Error fetching schedules:', error);
    }
  };

  useEffect(() => {
    fetchPsychometricians();
    fetchSchedules();
    fetchAllAppointments(); // Fetch all appointments for availability checking
  }, []);

  useEffect(() => {
    if (userData) {
      fetchAppointments();
    }
  }, [userData]);

  // Update upcoming and past appointments whenever appointments change
  useEffect(() => {
    if (appointments.length > 0) {
      const now = new Date();

      const upcoming = appointments.filter(appointment => {
        if (appointment.status !== 'scheduled') return false;

        // Create a date object from the appointment date and start time
        const appointmentDateTime = new Date(`${appointment.date}T${appointment.start_time}`);
        return appointmentDateTime > now;
      });

      const past = appointments.filter(appointment => {
        // Create a date object from the appointment date and start time
        const appointmentDateTime = new Date(`${appointment.date}T${appointment.start_time}`);
        return appointmentDateTime <= now;
      });

      console.log('Filtering appointments:', {
        total: appointments.length,
        upcoming: upcoming.length,
        past: past.length,
        appointments: appointments
      });

      setUpcomingAppointments(upcoming);
      setPastAppointments(past);
    } else {
      setUpcomingAppointments([]);
      setPastAppointments([]);
    }
  }, [appointments]);

  const fetchAppointments = async () => {
    if (!userData?.id) {
      console.log('No user data available for fetching appointments');
      return;
    }

    try {
      const { data, error } = await supabase
        .from('appointment')
        .select('*')
        .eq('user_id', userData.id)
        .order('date', { ascending: true });

      if (error) throw error;

      setAppointments(data || []);
    } catch (error) {
      console.error('Error fetching appointments:', error);
    }
  };

  // Fetch all appointments for availability checking (not user-specific)
  const fetchAllAppointments = async () => {
    try {
      const { data, error } = await supabase
        .from('appointment')
        .select('*')
        .execute();

      if (error) throw error;

      console.log('All appointments fetched for availability checking:', data?.length || 0);
      console.log('All appointments data:', data);
      setAllAppointments(data || []);
    } catch (error) {
      console.error('Error fetching all appointments:', error);
    }
  };

  const addAppointment = async (newAppointment) => {
    if (!userData?.id) {
      throw new Error('User not authenticated');
    }

    try {
      // First, check if the time slot is still available
      const timeSlot = {
        start_time: newAppointment.start_time,
        end_time: newAppointment.end_time
      };

      const isAvailable = isTimeSlotAvailable(
        newAppointment.date,
        timeSlot,
        parseInt(newAppointment.psychometricianId)
      );

      if (!isAvailable) {
        throw new Error('This time slot is no longer available. Please select a different time.');
      }

      const appointmentData = {
        user_id: userData.id, // Keep as string - database column needs to be VARCHAR
        psychometrician_id: parseInt(newAppointment.psychometricianId),
        date: newAppointment.date,
        start_time: newAppointment.start_time,
        end_time: newAppointment.end_time,
        status: 'scheduled',
        notes: newAppointment.notes || ''
      };

      console.log('Appointment data to insert:', appointmentData);
      console.log('Data types:', {
        user_id_type: typeof appointmentData.user_id,
        user_id_value: appointmentData.user_id,
        psychometrician_id_type: typeof appointmentData.psychometrician_id,
        psychometrician_id_value: appointmentData.psychometrician_id
      });

      const { data, error } = await supabase
        .from('appointment')
        .insert([appointmentData])
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Appointment created successfully:', data);

      // Immediately update both appointments and allAppointments state
      const createdAppointment = data[0];
      setAppointments([...appointments, createdAppointment]);
      setAllAppointments([...allAppointments, createdAppointment]);

      // Refresh appointments from database to ensure we have the latest data
      await fetchAppointments();
      await fetchAllAppointments(); // Also refresh all appointments for availability checking

      return createdAppointment;
    } catch (error) {
      console.error('Error adding appointment:', error);
      throw error;
    }
  };

  const updateAppointment = async (id, updatedData) => {
    try {
      const { error } = await supabase
        .from('appointment')
        .update({
          psychometrician_id: updatedData.psychometricianId,
          date: updatedData.date,
          start_time: updatedData.start_time,
          end_time: updatedData.end_time,
          status: updatedData.status,
          notes: updatedData.notes
        })
        .eq('id', id)
        .eq('user_id', userData.id);

      if (error) throw error;

      setAppointments(
        appointments.map(appointment =>
          appointment.id === id ? { ...appointment, ...updatedData } : appointment
        )
      );
    } catch (error) {
      console.error('Error updating appointment:', error);
      throw error;
    }
  };

  // Cancel an appointment
  const cancelAppointment = (appointmentId) => {
    const updatedAppointments = appointments.map(appointment =>
      appointment.id === appointmentId
        ? { ...appointment, status: 'cancelled' }
        : appointment
    );

    setAppointments(updatedAppointments);

    // Update upcoming appointments
    setUpcomingAppointments(
      updatedAppointments.filter(
        appointment =>
          appointment.status === 'scheduled' &&
          new Date(`${appointment.date}T${appointment.time}`) > new Date()
      )
    );

    return updatedAppointments.find(a => a.id === appointmentId);
  };

  // Reschedule an appointment
  const rescheduleAppointment = async (appointmentId, newDate, newTimeSlot) => {
    if (!userData?.id) {
      throw new Error('User not authenticated');
    }

    try {
      console.log('Reschedule params:', { appointmentId, newDate, newTimeSlot, userData: userData.id });

      // First, check if the new time slot is available (excluding the current appointment)
      const isAvailable = isTimeSlotAvailable(
        newDate,
        newTimeSlot,
        null, // Check for all psychometricians since we'll get it from existing appointment
        appointmentId
      );

      if (!isAvailable) {
        throw new Error('This time slot is no longer available. Please select a different time.');
      }

      const updatedData = {
        date: newDate,
        start_time: newTimeSlot.start_time,
        end_time: newTimeSlot.end_time
      };

      // Ensure appointmentId is a valid number
      const appointmentIdNum = parseInt(appointmentId);
      if (isNaN(appointmentIdNum)) {
        throw new Error('Invalid appointment ID');
      }

      console.log('Updating appointment with ID:', appointmentIdNum, 'Data:', updatedData);

      // Update in database - custom Supabase client only supports one .eq() filter
      // First verify this appointment belongs to the current user
      const existingAppointment = appointments.find(apt =>
        apt.id == appointmentIdNum && apt.user_id === userData.id
      );

      if (!existingAppointment) {
        throw new Error('Appointment not found or access denied');
      }

      const { data, error } = await supabase
        .from('appointment')
        .update(updatedData)
        .eq('id', appointmentIdNum)
        .execute();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Database update result:', data);

      // Update local state
      const updatedAppointments = appointments.map(appointment =>
        appointment.id == appointmentIdNum
          ? { ...appointment, ...updatedData }
          : appointment
      );

      setAppointments(updatedAppointments);

      // Refresh appointments from database to ensure consistency
      await fetchAppointments();
      await fetchAllAppointments(); // Also refresh all appointments for availability checking

      console.log('Appointment rescheduled successfully');
      return updatedAppointments.find(a => a.id == appointmentIdNum);
    } catch (error) {
      console.error('Error rescheduling appointment:', error);
      throw error;
    }
  };

  // Get available time slots for a psychometrician on a specific date
  const getTimeSlots = (psychometricianId = null, selectedDate = null) => {
    if (!psychometricianId || !selectedDate) {
      // Return default time slots if no specific psychometrician or date
      return [
        { start_time: '09:00', end_time: '10:00', display: '9:00 AM - 10:00 AM' },
        { start_time: '10:00', end_time: '11:00', display: '10:00 AM - 11:00 AM' },
        { start_time: '11:00', end_time: '12:00', display: '11:00 AM - 12:00 PM' },
        { start_time: '13:00', end_time: '14:00', display: '1:00 PM - 2:00 PM' },
        { start_time: '14:00', end_time: '15:00', display: '2:00 PM - 3:00 PM' },
        { start_time: '15:00', end_time: '16:00', display: '3:00 PM - 4:00 PM' },
        { start_time: '16:00', end_time: '17:00', display: '4:00 PM - 5:00 PM' }
      ];
    }

    // Get day of week from selected date
    const dayOfWeek = new Date(selectedDate).toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

    // Find schedules for this psychometrician and day
    const psychometricianSchedules = schedules.filter(schedule =>
      schedule.psychometrician_id === psychometricianId &&
      schedule.day_of_week === dayOfWeek
    );

    if (psychometricianSchedules.length === 0) {
      return []; // No schedules available for this day
    }

    // Generate time slots based on schedules
    const timeSlots = [];
    psychometricianSchedules.forEach(schedule => {
      const startTime = schedule.start_time;
      const endTime = schedule.end_time;

      // Convert time strings to minutes for easier calculation
      const startMinutes = timeToMinutes(startTime);
      const endMinutes = timeToMinutes(endTime);

      // Generate 1-hour slots
      for (let minutes = startMinutes; minutes < endMinutes; minutes += 60) {
        const slotStart = minutesToTime(minutes);
        const slotEnd = minutesToTime(minutes + 60);

        timeSlots.push({
          start_time: slotStart,
          end_time: slotEnd,
          display: formatTimeSlot(slotStart, slotEnd)
        });
      }
    });

    return timeSlots;
  };

  // Helper function to convert time string to minutes
  const timeToMinutes = (timeStr) => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  };

  // Helper function to convert minutes to time string
  const minutesToTime = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };

  // Helper function to format time slot for display
  const formatTimeSlot = (startTime, endTime) => {
    const formatTime = (time) => {
      const [hours, minutes] = time.split(':').map(Number);
      const period = hours >= 12 ? 'PM' : 'AM';
      const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours;
      return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
    };

    return `${formatTime(startTime)} - ${formatTime(endTime)}`;
  };

  // Book a new appointment
  const bookAppointment = (psychometricianId, date, timeSlot, notes = '') => {
    // Validate inputs
    if (!psychometricianId || !date || !timeSlot) {
      throw new Error('Missing required appointment information');
    }

    // Validate timeSlot object
    if (!timeSlot.start_time || !timeSlot.end_time) {
      console.error('Invalid timeSlot object:', timeSlot);
      throw new Error('Invalid time slot: missing start_time or end_time');
    }

    // Format date if it's a Date object
    const formattedDate = date instanceof Date ? formatDateString(date) : date;

    // Create and add the appointment
    const appointment = addAppointment({
      psychometricianId,
      date: formattedDate,
      start_time: timeSlot.start_time,
      end_time: timeSlot.end_time,
      notes
    });

    return appointment;
  };

  // Get psychometrician details by ID
  const getPsychometricianById = (psychometricianId) => {
    return psychometricians.find(psychometrician => psychometrician.id === psychometricianId);
  };

  // Get upcoming appointments with psychometrician details
  const getUpcomingAppointments = () => {
    return upcomingAppointments.map(appointment => ({
      ...appointment,
      psychometrician: getPsychometricianById(appointment.psychometrician_id)
    }));
  };

  // Get past appointments with psychometrician details
  const getPastAppointments = () => {
    return pastAppointments.map(appointment => ({
      ...appointment,
      psychometrician: getPsychometricianById(appointment.psychometrician_id)
    }));
  };

  // Check if a specific time slot is available
  const isTimeSlotAvailable = (date, timeSlot, psychometricianId = null, excludeAppointmentId = null) => {
    // Validate inputs
    if (!date || !timeSlot) {
      console.error('isTimeSlotAvailable: Missing required parameters', { date, timeSlot });
      return false;
    }

    // Validate timeSlot object
    if (!timeSlot.start_time || !timeSlot.end_time) {
      console.error('isTimeSlotAvailable: Invalid timeSlot object', timeSlot);
      return false;
    }

    // Ensure allAppointments is available
    if (!allAppointments || !Array.isArray(allAppointments)) {
      console.warn('isTimeSlotAvailable: allAppointments not available, assuming slot is available');
      return true; // If we can't check, assume it's available
    }

    // Format date if it's a Date object
    const formattedDate = date instanceof Date ? formatDateString(date) : date;

    // Convert excludeAppointmentId to number for comparison
    const excludeId = excludeAppointmentId ? parseInt(excludeAppointmentId) : null;

    // Check for ANY overlapping appointments using ALL appointments (not just user's appointments)
    const hasConflict = allAppointments.some(appointment => {
      // Validate appointment object - be more thorough
      if (!appointment) {
        console.warn('isTimeSlotAvailable: Null appointment object');
        return false;
      }

      if (typeof appointment !== 'object') {
        console.warn('isTimeSlotAvailable: Appointment is not an object', appointment);
        return false;
      }

      if (!appointment.start_time || !appointment.end_time) {
        console.warn('isTimeSlotAvailable: Invalid appointment object missing time properties', appointment);
        return false;
      }

      // Skip if this is the appointment being excluded (for rescheduling)
      if (appointment.id === excludeId) return false;

      // Only check scheduled appointments
      if (appointment.status !== 'scheduled' && appointment.status !== 'rescheduled') return false;

      // Check if it's the same date
      if (appointment.date !== formattedDate) return false;

      // Check if it's the same psychometrician (if specified)
      if (psychometricianId && appointment.psychometrician_id !== psychometricianId) return false;

      // Normalize time formats to ensure consistent comparison
      // Database times might have seconds (HH:MM:SS) while UI times don't (HH:MM)
      const normalizeTime = (time) => {
        if (!time) return '';
        return time.substring(0, 5); // "HH:MM:SS" -> "HH:MM" or "HH:MM" -> "HH:MM"
      };

      const appointmentStart = normalizeTime(appointment.start_time);
      const appointmentEnd = normalizeTime(appointment.end_time);
      const slotStart = normalizeTime(timeSlot.start_time);
      const slotEnd = normalizeTime(timeSlot.end_time);

      // Two time ranges overlap if: start1 < end2 AND start2 < end1
      // Adjacent slots (e.g., 10:00-11:00 and 11:00-12:00) should NOT overlap
      const hasTimeOverlap = slotStart < appointmentEnd && appointmentStart < slotEnd;

      return hasTimeOverlap;
    });

    return !hasConflict;
  };

  // Context value with all appointment functions and data
  const contextValue = {
    bookAppointment,
    updateAppointment,
    cancelAppointment,
    rescheduleAppointment,
    getPsychometricianById,
    getUpcomingAppointments,
    getPastAppointments,
    isTimeSlotAvailable,
    psychometricians,
    schedules,
    appointments,
    allAppointments,
    upcomingAppointments,
    pastAppointments,
    getDates,
    getTimeSlots,
    formatDate,
    formatDateString,
    fetchPsychometricians,
    fetchSchedules,
    fetchAppointments,
    fetchAllAppointments
  };

  return (
    <AppointmentContext.Provider value={contextValue}>
      {children}
    </AppointmentContext.Provider>
  );
};

// Custom hook to use the context
export const useAppointment = () => {
  const context = useContext(AppointmentContext);
  if (!context) {
    throw new Error('useAppointment must be used within an AppointmentProvider');
  }
  return context;
};

export default AppointmentContext;


