import React, { useRef, useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Platform,
  StatusBar,
  Image,
  Easing
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import BottomNavigation from './components/BottomNavigation';
import CustomStatusBar from './components/CustomStatusBar';
import { useUser } from './context/UserContext';

const Dashboard = () => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideUpAnim = useRef(new Animated.Value(50)).current;

  // Mood emoji animations - individual animations for each emoji
  const moodAnimations = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0)
  ]).current;

  const router = useRouter();
  const { userData, toggleHidePersonalInfo } = useUser();

  // State management
  const [selectedMood, setSelectedMood] = useState(null);
  const [showMoodTracker, setShowMoodTracker] = useState(true);
  const [moodSaved, setMoodSaved] = useState(false);

  // Mood options matching the reference image exactly
  const moodOptions = [
    { emoji: '😔', label: 'Sad' },
    { emoji: '😕', label: 'Meh' },
    { emoji: '😐', label: 'Okay' },
    { emoji: '🙂', label: 'Good' },
    { emoji: '😄', label: 'Great' }
  ];

  // Sample appointment data
  const upcomingAppointment = {
    doctor: 'Dr. Abie Rufino',
    specialty: 'Psychometrician',
    date: 'May 25, 2025',
    time: '8:00 am',
    initial: 'A'
  };

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideUpAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Start emoji pop-up animations with staggered timing
    const animateEmojis = () => {
      const animations = moodAnimations.map((anim, index) =>
        Animated.sequence([
          Animated.delay(index * 150), // Stagger each emoji by 150ms
          Animated.spring(anim, {
            toValue: 1,
            tension: 100,
            friction: 8,
            useNativeDriver: true,
          })
        ])
      );

      Animated.parallel(animations).start();
    };

    // Start emoji animations after main content loads
    setTimeout(animateEmojis, 1000);
  }, []);

  const handleMoodSelect = (index) => {
    setSelectedMood(index);

    // Animate selected emoji with a bounce effect
    Animated.sequence([
      Animated.spring(moodAnimations[index], {
        toValue: 1.3,
        tension: 100,
        friction: 3,
        useNativeDriver: true,
      }),
      Animated.spring(moodAnimations[index], {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      })
    ]).start();
  };

  const handleSaveMood = () => {
    if (selectedMood !== null) {
      setMoodSaved(true);

      // Hide mood tracker with animation after saving
      setTimeout(() => {
        setShowMoodTracker(false);
      }, 500);

      // Navigate to mood journal
      setTimeout(() => {
        router.push({
          pathname: '/mood-journal',
          params: {
            mood: moodOptions[selectedMood].emoji,
            moodIndex: selectedMood
          }
        });
      }, 1000);
    }
  };

  const handleProfilePress = () => {
    router.push('/user-profile');
  };

  const getCurrentDate = () => {
    const today = new Date();
    return today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <View style={styles.container}>
      <CustomStatusBar backgroundColor="transparent" barStyle="light-content" />

      <SafeAreaView style={styles.safeArea}>
        {/* Curved Header with Gradient - Exactly Matching Reference Design */}
        <LinearGradient
          colors={['#6B9B7A', '#8BB89C']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <View style={styles.userInfoContainer}>
              <TouchableOpacity
                style={styles.profilePicture}
                onPress={handleProfilePress}
              >
                <Text style={styles.profileInitial}>
                  {(userData.firstName || 'L').charAt(0)}
                </Text>
              </TouchableOpacity>
              <Text style={styles.userName}>
                {userData.hidePersonalInfo ? '2021*******' : (userData.controlNumber || '2021*******')}
              </Text>
            </View>
            <TouchableOpacity style={styles.notificationButton}>
              <View style={styles.notificationIcon}>
                <View style={styles.notificationBell} />
                <View style={styles.notificationBadge} />
              </View>
            </TouchableOpacity>
          </View>
        </LinearGradient>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Mood Tracker Card - Exactly Matching Reference Image */}
          {showMoodTracker && (
            <Animated.View
              style={[
                styles.moodCard,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideUpAnim }]
                }
              ]}
            >
              <View style={styles.moodCardContent}>
                <Text style={styles.moodTitle}>How are you feeling today?</Text>
                <View style={styles.moodDateContainer}>
                  <Text style={styles.moodSubtitle}>Today's mood</Text>
                  <Text style={styles.moodDate}>May 25, 2025</Text>
                </View>

                <View style={styles.moodOptions}>
                  {moodOptions.map((mood, index) => (
                    <TouchableOpacity
                      key={index}
                      style={styles.moodOption}
                      onPress={() => handleMoodSelect(index)}
                    >
                      <Animated.View
                        style={[
                          styles.moodEmojiContainer,
                          {
                            transform: [
                              { scale: moodAnimations[index] },
                              {
                                translateY: moodAnimations[index].interpolate({
                                  inputRange: [0, 1],
                                  outputRange: [20, 0]
                                })
                              }
                            ],
                            opacity: moodAnimations[index]
                          }
                        ]}
                      >
                        <Text style={[
                          styles.moodEmoji,
                          selectedMood === index && styles.selectedMoodEmoji
                        ]}>
                          {mood.emoji}
                        </Text>
                      </Animated.View>
                      <Text style={[
                        styles.moodLabel,
                        selectedMood === index && styles.selectedMoodLabel
                      ]}>
                        {mood.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>

                <TouchableOpacity
                  style={[
                    styles.saveMoodButton,
                    selectedMood === null && styles.saveMoodButtonDisabled
                  ]}
                  onPress={handleSaveMood}
                  disabled={selectedMood === null}
                >
                  <Text style={[
                    styles.saveMoodButtonText,
                    selectedMood === null && styles.saveMoodButtonTextDisabled
                  ]}>
                    Save Mood
                  </Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          )}



          {/* Upcoming Appointment Section - Matching Reference Design */}
          <View style={styles.appointmentSection}>
            <Text style={styles.appointmentSectionTitle}>Upcoming Appointment</Text>

            <Animated.View
              style={[
                styles.appointmentCard,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideUpAnim }]
                }
              ]}
            >
              <View style={styles.appointmentContent}>
                <View style={styles.doctorInfo}>
                  <View style={styles.doctorAvatar}>
                    <Text style={styles.doctorInitial}>A</Text>
                  </View>
                  <View style={styles.doctorDetails}>
                    <Text style={styles.doctorName}>Dr. Abie Rufino</Text>
                    <Text style={styles.doctorSpecialty}>Psychometrician</Text>
                  </View>
                </View>
                <View style={styles.appointmentTime}>
                  <Text style={styles.appointmentDate}>May 25, 2025</Text>
                  <Text style={styles.appointmentTimeText}>8:00 am</Text>
                </View>
              </View>
              <TouchableOpacity
                style={styles.rescheduleButton}
                onPress={() => router.push('/reschedule-appointment')}
              >
                <Text style={styles.rescheduleButtonText}>Reschedule</Text>
              </TouchableOpacity>
            </Animated.View>
          </View>

          {/* AI Mental Health Assistant Card - Exactly Matching Reference */}
          <Animated.View
            style={[
              styles.aiAssistantCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.aiAssistantContent}>
              <View style={styles.aiAssistantHeader}>
                <View style={styles.aiIcon}>
                  <Text style={styles.aiIconText}>💬</Text>
                </View>
                <Text style={styles.aiAssistantTitle}>AI Mental Health Assistant</Text>
              </View>
              <Text style={styles.aiAssistantSubtitle}>
                Feeling overwhelmed or need someone to talk to?
              </Text>
              <Text style={styles.aiAssistantDescription}>
                Our AI assistant is here to listen, provide support, and offer guidance 24/7
              </Text>
              <TouchableOpacity
                style={styles.chatButton}
                onPress={() => router.push('/ai-chatbot')}
              >
                <Text style={styles.chatButtonText}>Chat with Aira</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>

          {/* Mental Health Assessment Card - Exactly Matching Reference */}
          <Animated.View
            style={[
              styles.assessmentCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <LinearGradient
              colors={['#6B9B7A', '#8BB89C']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.assessmentGradient}
            >
              <Text style={styles.assessmentTitle}>Mental Health Assessment</Text>
            </LinearGradient>
          </Animated.View>


        </ScrollView>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  safeArea: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 20,
    paddingBottom: 120,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 50,
    borderBottomRightRadius: 50,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profilePicture: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E67E22',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  profileInitial: {
    fontSize: 22,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  userName: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  notificationButton: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationIcon: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationBell: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  notificationBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#E74C3C',
  },
  scrollContent: {
    padding: 20,
    paddingTop: -70,
    paddingBottom: 100,
  },
  moodCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 40,
    marginBottom: 25,
    marginTop: -70,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 12,
  },
  moodCardContent: {
    padding: 30,
    paddingBottom: 35,
  },
  moodTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 12,
  },
  moodDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 30,
  },
  moodSubtitle: {
    fontSize: 16,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  moodDate: {
    fontSize: 16,
    color: '#2C3E50',
    fontWeight: '600',
  },
  moodOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 35,
    paddingHorizontal: 5,
  },
  moodOption: {
    alignItems: 'center',
    flex: 1,
  },
  moodEmojiContainer: {
    marginBottom: 12,
  },
  moodEmoji: {
    fontSize: 45,
  },
  selectedMoodEmoji: {
    fontSize: 50,
  },
  moodLabel: {
    fontSize: 14,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  selectedMoodLabel: {
    color: '#6B9B7A',
    fontWeight: 'bold',
  },
  saveMoodButton: {
    backgroundColor: '#2C5530',
    borderRadius: 30,
    paddingVertical: 18,
    alignItems: 'center',
    marginHorizontal: 20,
  },
  saveMoodButtonDisabled: {
    backgroundColor: '#BDC3C7',
  },
  saveMoodButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
  },
  saveMoodButtonTextDisabled: {
    color: '#7F8C8D',
  },

  appointmentSection: {
    marginBottom: 25,
  },
  appointmentSectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 15,
    marginLeft: 5,
  },
  appointmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    marginBottom: 20,
    padding: 20,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  appointmentContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 18,
  },
  doctorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  doctorAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E67E22',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  doctorInitial: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  doctorDetails: {
    flex: 1,
  },
  doctorName: {
    fontSize: 17,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 3,
  },
  doctorSpecialty: {
    fontSize: 14,
    color: '#7F8C8D',
  },
  appointmentTime: {
    alignItems: 'flex-end',
  },
  appointmentDate: {
    fontSize: 14,
    color: '#2C3E50',
    fontWeight: '600',
    marginBottom: 2,
  },
  appointmentTimeText: {
    fontSize: 14,
    color: '#7F8C8D',
  },
  rescheduleButton: {
    backgroundColor: '#95A5A6',
    borderRadius: 25,
    paddingVertical: 14,
    alignItems: 'center',
    marginHorizontal: 0,
  },
  rescheduleButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  aiAssistantCard: {
    backgroundColor: '#E8F5E8',
    borderRadius: 25,
    marginBottom: 25,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  aiAssistantContent: {
    padding: 25,
  },
  aiAssistantHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  aiIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  aiIconText: {
    fontSize: 20,
  },
  aiAssistantTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  aiAssistantSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 10,
  },
  aiAssistantDescription: {
    fontSize: 14,
    color: '#7F8C8D',
    lineHeight: 22,
    marginBottom: 20,
  },
  chatButton: {
    backgroundColor: '#6B9B7A',
    borderRadius: 25,
    paddingVertical: 14,
    alignItems: 'center',
    marginHorizontal: 0,
  },
  chatButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  assessmentCard: {
    borderRadius: 20,
    marginBottom: 20,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  assessmentGradient: {
    padding: 20,
    borderRadius: 20,
  },
  assessmentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 10,
  },
  assessmentDescription: {
    fontSize: 14,
    color: '#FFFFFF',
    lineHeight: 20,
    marginBottom: 15,
    opacity: 0.9,
  },
  startAssessmentButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingVertical: 12,
    alignItems: 'center',
    marginHorizontal: 0,
  },
  startAssessmentButtonText: {
    color: '#7BA05B',
    fontSize: 15,
    fontWeight: '600',
  },
});

export default Dashboard;
