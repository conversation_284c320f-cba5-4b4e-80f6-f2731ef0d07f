import React, { useRef, useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Platform,
  StatusBar,
  Image,
  Easing
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import BottomNavigation from './components/BottomNavigation';
import CustomStatusBar from './components/CustomStatusBar';
import { useUser } from './context/UserContext';

const Dashboard = () => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideUpAnim = useRef(new Animated.Value(50)).current;

  // Mood emoji animations - individual animations for each emoji
  const moodAnimations = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0)
  ]).current;

  const router = useRouter();
  const { userData, toggleHidePersonalInfo } = useUser();

  // State management
  const [selectedMood, setSelectedMood] = useState(null);
  const [showMoodTracker, setShowMoodTracker] = useState(true);
  const [moodSaved, setMoodSaved] = useState(false);

  // Mood options matching the reference image exactly
  const moodOptions = [
    { emoji: '😔', label: 'Sad' },
    { emoji: '😕', label: 'Meh' },
    { emoji: '😐', label: 'Okay' },
    { emoji: '🙂', label: 'Good' },
    { emoji: '😄', label: 'Great' }
  ];

  // Sample appointment data
  const upcomingAppointment = {
    doctor: 'Dr. Abie Rufino',
    specialty: 'Psychometrician',
    date: 'May 25, 2025',
    time: '8:00 am',
    initial: 'A'
  };

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideUpAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Start emoji pop-up animations with staggered timing
    const animateEmojis = () => {
      const animations = moodAnimations.map((anim, index) =>
        Animated.sequence([
          Animated.delay(index * 150), // Stagger each emoji by 150ms
          Animated.spring(anim, {
            toValue: 1,
            tension: 100,
            friction: 8,
            useNativeDriver: true,
          })
        ])
      );

      Animated.parallel(animations).start();
    };

    // Start emoji animations after main content loads
    setTimeout(animateEmojis, 1000);
  }, []);

  const handleMoodSelect = (index) => {
    setSelectedMood(index);

    // Animate selected emoji with a bounce effect
    Animated.sequence([
      Animated.spring(moodAnimations[index], {
        toValue: 1.3,
        tension: 100,
        friction: 3,
        useNativeDriver: true,
      }),
      Animated.spring(moodAnimations[index], {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      })
    ]).start();
  };

  const handleSaveMood = () => {
    if (selectedMood !== null) {
      setMoodSaved(true);

      // Hide mood tracker with animation after saving
      setTimeout(() => {
        setShowMoodTracker(false);
      }, 500);

      // Navigate to mood journal
      setTimeout(() => {
        router.push({
          pathname: '/mood-journal',
          params: {
            mood: moodOptions[selectedMood].emoji,
            moodIndex: selectedMood
          }
        });
      }, 1000);
    }
  };

  const handleProfilePress = () => {
    router.push('/user-profile');
  };

  const getCurrentDate = () => {
    const today = new Date();
    return today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <View style={styles.container}>
      <CustomStatusBar backgroundColor="transparent" barStyle="light-content" />

      <SafeAreaView style={styles.safeArea}>
        {/* Curved Header with Gradient - Matching Reference Design */}
        <LinearGradient
          colors={['#7BA05B', '#9BC76D']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <View style={styles.userInfoContainer}>
              <TouchableOpacity
                style={styles.profilePicture}
                onPress={handleProfilePress}
              >
                <Text style={styles.profileInitial}>
                  {(userData.firstName || 'L').charAt(0)}
                </Text>
              </TouchableOpacity>
              <Text style={styles.userName}>
                {userData.hidePersonalInfo ? '2021*******' : (userData.controlNumber || '2021*******')}
              </Text>
            </View>
            <TouchableOpacity style={styles.notificationButton}>
              <View style={styles.notificationIcon}>
                <Text style={styles.notificationIconText}>🔔</Text>
                <View style={styles.notificationBadge} />
              </View>
            </TouchableOpacity>
          </View>
        </LinearGradient>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Mood Tracker Card - Redesigned to Match Reference Image Exactly */}
          {showMoodTracker && (
            <Animated.View
              style={[
                styles.moodCard,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideUpAnim }]
                }
              ]}
            >
              <View style={styles.moodCardContent}>
                <Text style={styles.moodTitle}>How are you feeling today?</Text>
                <View style={styles.moodDateContainer}>
                  <Text style={styles.moodSubtitle}>Today's mood</Text>
                  <Text style={styles.moodDate}>{getCurrentDate()}</Text>
                </View>

                <View style={styles.moodOptions}>
                  {moodOptions.map((mood, index) => (
                    <TouchableOpacity
                      key={index}
                      style={styles.moodOption}
                      onPress={() => handleMoodSelect(index)}
                    >
                      <Animated.View
                        style={[
                          styles.moodEmojiContainer,
                          {
                            transform: [
                              { scale: moodAnimations[index] },
                              {
                                translateY: moodAnimations[index].interpolate({
                                  inputRange: [0, 1],
                                  outputRange: [20, 0]
                                })
                              }
                            ],
                            opacity: moodAnimations[index]
                          }
                        ]}
                      >
                        <Text style={[
                          styles.moodEmoji,
                          selectedMood === index && styles.selectedMoodEmoji
                        ]}>
                          {mood.emoji}
                        </Text>
                      </Animated.View>
                      <Text style={[
                        styles.moodLabel,
                        selectedMood === index && styles.selectedMoodLabel
                      ]}>
                        {mood.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>

                <TouchableOpacity
                  style={[
                    styles.saveMoodButton,
                    selectedMood === null && styles.saveMoodButtonDisabled
                  ]}
                  onPress={handleSaveMood}
                  disabled={selectedMood === null}
                >
                  <Text style={[
                    styles.saveMoodButtonText,
                    selectedMood === null && styles.saveMoodButtonTextDisabled
                  ]}>
                    Save Mood
                  </Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          )}



          {/* Upcoming Appointment Card */}
          <Animated.View
            style={[
              styles.appointmentCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <Text style={styles.appointmentTitle}>Upcoming Appointment</Text>
            <View style={styles.appointmentContent}>
              <View style={styles.doctorInfo}>
                <View style={styles.doctorAvatar}>
                  <Text style={styles.doctorInitial}>{upcomingAppointment.initial}</Text>
                </View>
                <View style={styles.doctorDetails}>
                  <Text style={styles.doctorName}>{upcomingAppointment.doctor}</Text>
                  <Text style={styles.doctorSpecialty}>{upcomingAppointment.specialty}</Text>
                </View>
              </View>
              <View style={styles.appointmentTime}>
                <Text style={styles.appointmentDate}>{upcomingAppointment.date}</Text>
                <Text style={styles.appointmentTimeText}>{upcomingAppointment.time}</Text>
              </View>
            </View>
            <TouchableOpacity
              style={styles.rescheduleButton}
              onPress={() => router.push('/reschedule-appointment')}
            >
              <Text style={styles.rescheduleButtonText}>Reschedule</Text>
            </TouchableOpacity>
          </Animated.View>

          {/* AI Mental Health Assistant Card - Redesigned to Match Reference */}
          <Animated.View
            style={[
              styles.aiAssistantCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.aiAssistantContent}>
              <View style={styles.aiAssistantHeader}>
                <View style={styles.aiIcon}>
                  <Text style={styles.aiIconText}>💬</Text>
                </View>
                <Text style={styles.aiAssistantTitle}>AI Mental Health Assistant</Text>
              </View>
              <Text style={styles.aiAssistantSubtitle}>
                Feeling overwhelmed or need someone to talk to?
              </Text>
              <Text style={styles.aiAssistantDescription}>
                Our AI assistant is here to listen, provide support, and offer guidance 24/7
              </Text>
              <TouchableOpacity
                style={styles.chatButton}
                onPress={() => router.push('/ai-chatbot')}
              >
                <Text style={styles.chatButtonText}>Chat with Aira</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>

          {/* Mental Health Assessment Card - Redesigned */}
          <Animated.View
            style={[
              styles.assessmentCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <LinearGradient
              colors={['#7BA05B', '#9BC76D']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.assessmentGradient}
            >
              <Text style={styles.assessmentTitle}>Mental Health Assessment</Text>
              <Text style={styles.assessmentDescription}>
                Take a quick assessment to better understand your current{'\n'}mental health state and get personalized recommendations
              </Text>
              <TouchableOpacity
                style={styles.startAssessmentButton}
                onPress={() => router.push('/mental-assessment')}
              >
                <Text style={styles.startAssessmentButtonText}>Start self-assessment</Text>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>


        </ScrollView>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  safeArea: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 20,
    paddingBottom: 80,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 40,
    borderBottomRightRadius: 40,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profilePicture: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    backgroundColor: '#D4A574',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  profileInitial: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  userName: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationIcon: {
    position: 'relative',
  },
  notificationIconText: {
    fontSize: 18,
    color: '#FFFFFF',
  },
  notificationBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF4444',
  },
  scrollContent: {
    padding: 20,
    paddingTop: -50,
    paddingBottom: 100,
  },
  moodCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 30,
    marginBottom: 20,
    marginTop: -50,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 8,
  },
  moodCardContent: {
    padding: 25,
  },
  moodTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  moodDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 25,
  },
  moodSubtitle: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
  moodDate: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '600',
  },
  moodOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
    paddingHorizontal: 10,
  },
  moodOption: {
    alignItems: 'center',
    flex: 1,
  },
  moodEmojiContainer: {
    marginBottom: 8,
  },
  moodEmoji: {
    fontSize: 40,
  },
  selectedMoodEmoji: {
    fontSize: 44,
  },
  moodLabel: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  selectedMoodLabel: {
    color: '#7BA05B',
    fontWeight: 'bold',
  },
  saveMoodButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    marginHorizontal: 0,
  },
  saveMoodButtonDisabled: {
    backgroundColor: '#E0E0E0',
  },
  saveMoodButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  saveMoodButtonTextDisabled: {
    color: '#999999',
  },

  appointmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 20,
    padding: 20,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  appointmentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 15,
  },
  appointmentContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  doctorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  doctorAvatar: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    backgroundColor: '#D4A574',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  doctorInitial: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  doctorDetails: {
    flex: 1,
  },
  doctorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 2,
  },
  doctorSpecialty: {
    fontSize: 14,
    color: '#666666',
  },
  appointmentTime: {
    alignItems: 'flex-end',
  },
  appointmentDate: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
    marginBottom: 2,
  },
  appointmentTimeText: {
    fontSize: 14,
    color: '#666666',
  },
  rescheduleButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 20,
    paddingVertical: 12,
    alignItems: 'center',
    marginHorizontal: 0,
  },
  rescheduleButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  aiAssistantCard: {
    backgroundColor: '#E8F5E8',
    borderRadius: 20,
    marginBottom: 20,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  aiAssistantContent: {
    padding: 20,
  },
  aiAssistantHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  aiIcon: {
    width: 35,
    height: 35,
    borderRadius: 17.5,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  aiIconText: {
    fontSize: 18,
  },
  aiAssistantTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  aiAssistantSubtitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  aiAssistantDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 15,
  },
  chatButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 20,
    paddingVertical: 12,
    alignItems: 'center',
    marginHorizontal: 0,
  },
  chatButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  assessmentCard: {
    borderRadius: 30,
    marginBottom: 25,
    marginHorizontal: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 20,
    elevation: 15,
  },
  assessmentGradient: {
    padding: 30,
    borderRadius: 30,
  },
  assessmentTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  assessmentDescription: {
    fontSize: 14,
    color: '#FFFFFF',
    lineHeight: 20,
    marginBottom: 20,
    opacity: 0.9,
  },
  startAssessmentButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 30,
    paddingVertical: 14,
    alignItems: 'center',
    marginHorizontal: 15,
  },
  startAssessmentButtonText: {
    color: '#2C5530',
    fontSize: 16,
    fontWeight: '700',
  },
  stressManagementCard: {
    backgroundColor: '#F0F8F0',
    borderRadius: 30,
    marginBottom: 25,
    marginHorizontal: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 18,
    elevation: 10,
  },
  stressManagementContent: {
    padding: 25,
  },
  stressManagementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  stressIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stressIconText: {
    fontSize: 20,
  },
  stressManagementTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C5530',
  },
  stressManagementDescription: {
    fontSize: 14,
    color: '#5A6B5A',
    lineHeight: 22,
    marginBottom: 25,
  },
  startJournalingButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 30,
    paddingVertical: 14,
    alignItems: 'center',
    marginHorizontal: 15,
  },
  startJournalingButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
  },
});

export default Dashboard;
