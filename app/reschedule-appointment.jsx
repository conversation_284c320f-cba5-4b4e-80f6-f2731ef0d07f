import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Modal,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useAppointment } from './context/AppointmentContext';
import CustomStatusBar from './components/CustomStatusBar';

const RescheduleAppointment = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const appointmentId = params.id;

  const {
    appointments,
    rescheduleAppointment,
    isTimeSlotAvailable,
    getPsychometricianById,
    getDates,
    getTimeSlots,
    formatDate,
    formatDateString
  } = useAppointment();

  // Find the appointment to reschedule
  const [appointment, setAppointment] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  // Load the appointment details
  useEffect(() => {
    if (appointmentId) {
      const foundAppointment = appointments.find(a => a.id == appointmentId);
      if (foundAppointment) {
        setAppointment(foundAppointment);
        setSelectedDate(foundAppointment.date);
        // Create time slot object from start_time and end_time
        const timeSlot = {
          start_time: foundAppointment.start_time,
          end_time: foundAppointment.end_time,
          display: `${foundAppointment.start_time} - ${foundAppointment.end_time}`
        };
        setSelectedTime(timeSlot);
      } else {
        // Handle appointment not found
        alert('Appointment not found');
        router.push('/appointments');
      }
    } else {
      // No appointment ID provided
      alert('No appointment specified');
      router.push('/appointments');
    }
  }, [appointmentId, appointments]);

  const handleRescheduleAppointment = () => {
    if (selectedDate && selectedTime) {
      // Check if the date or time has actually changed
      const originalTimeSlot = {
        start_time: appointment.start_time,
        end_time: appointment.end_time
      };

      if (selectedDate === appointment.date &&
          selectedTime.start_time === originalTimeSlot.start_time &&
          selectedTime.end_time === originalTimeSlot.end_time) {
        alert('Please select a different date or time to reschedule');
        return;
      }

      // Check if the selected time slot is available
      if (!isTimeSlotAvailable(selectedDate, selectedTime, appointment.psychometrician_id, appointmentId)) {
        alert('This time slot is not available');
        return;
      }

      setShowConfirmModal(true);
    }
  };

  const confirmReschedule = async () => {
    try {
      // Call the reschedule function from the controller
      await rescheduleAppointment(appointmentId, selectedDate, selectedTime);

      setShowConfirmModal(false);
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Error rescheduling appointment:', error);
      alert('Failed to reschedule appointment');
    }
  };

  const closeSuccessModal = () => {
    setShowSuccessModal(false);
    router.push('/appointments');
  };

  // If appointment is not loaded yet, show loading state
  if (!appointment) {
    return (
      <SafeAreaView style={styles.container}>
        <CustomStatusBar backgroundColor="#6B9142" barStyle="light-content" />
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
            activeOpacity={0.7}
          >
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Reschedule Appointment</Text>
          <View style={styles.headerDecoration} />
        </View>
        <View style={styles.loadingContainer}>
          <View style={styles.loadingIndicator}>
            <Text style={styles.loadingEmoji}>⏳</Text>
          </View>
          <Text style={styles.loadingText}>Loading appointment details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Get psychometrician details
  const psychometrician = getPsychometricianById(appointment.psychometrician_id);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={styles.container}>
          <CustomStatusBar backgroundColor="#6B9142" barStyle="light-content" />

          <View style={styles.header}>
            <TouchableOpacity
              onPress={() => router.back()}
              style={styles.backButton}
              activeOpacity={0.7}
            >
              <Text style={styles.backButtonText}>←</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Reschedule Appointment</Text>
            <View style={styles.headerDecoration} />
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <View style={styles.appointmentCard}>
              <View style={styles.therapistInfo}>
                <Text style={styles.therapistImage}>{psychometrician?.image || '👨‍⚕️'}</Text>
                <View>
                  <Text style={styles.therapistName}>{psychometrician?.name || 'Psychometrician'}</Text>
                  <Text style={styles.therapistSpecialty}>{psychometrician?.specialty || 'Mental Health Professional'}</Text>
                </View>
              </View>
              <Text style={styles.currentAppointmentText}>
                Current: {appointment.date} at {appointment.start_time} - {appointment.end_time}
              </Text>
            </View>

            <Text style={styles.sectionTitle}>Select a New Date</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.datesContainer}
            >
              {getDates().map((date, index) => {
                const formattedDate = formatDate(date);
                const dateString = formatDateString(date);
                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.dateCard,
                      selectedDate === dateString && styles.selectedDateCard
                    ]}
                    onPress={() => setSelectedDate(dateString)}
                  >
                    <Text style={styles.dateDay}>{formattedDate.day}</Text>
                    <Text style={styles.dateNumber}>{formattedDate.date}</Text>
                    <Text style={styles.dateMonth}>{formattedDate.month}</Text>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>

            <Text style={styles.sectionTitle}>Select a New Time</Text>
            <View style={styles.timeContainer}>
              {getTimeSlots(appointment.psychometrician_id, selectedDate).map((timeSlot, index) => {
                const available = isTimeSlotAvailable(selectedDate, timeSlot, appointment.psychometrician_id, appointmentId);
                const isSelected = selectedTime &&
                  selectedTime.start_time === timeSlot.start_time &&
                  selectedTime.end_time === timeSlot.end_time;

                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.timeCard,
                      isSelected && styles.selectedTimeCard,
                      !available && styles.unavailableTimeCard
                    ]}
                    onPress={() => available && setSelectedTime(timeSlot)}
                    disabled={!available}
                  >
                    <Text
                      style={[
                        styles.timeText,
                        isSelected && styles.selectedTimeText,
                        !available && styles.unavailableTimeText
                      ]}
                    >
                      {timeSlot.display}
                    </Text>
                    {!available && (
                      <Text style={styles.bookedText}>Booked</Text>
                    )}
                  </TouchableOpacity>
                );
              })}
            </View>

            <TouchableOpacity
              style={[
                styles.rescheduleButton,
                (!selectedDate || !selectedTime) && styles.disabledButton
              ]}
              onPress={handleRescheduleAppointment}
              disabled={!selectedDate || !selectedTime}
            >
              <Text style={styles.rescheduleButtonText}>Reschedule Appointment</Text>
            </TouchableOpacity>
          </ScrollView>

          {/* Confirmation Modal */}
          <Modal
            visible={showConfirmModal}
            transparent={true}
            animationType="fade"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Confirm Reschedule</Text>
                <Text style={styles.modalText}>
                  Are you sure you want to reschedule your appointment with {psychometrician?.name || 'Psychometrician'} to {selectedDate} at {selectedTime?.display || 'selected time'}?
                </Text>
                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={styles.modalCancelButton}
                    onPress={() => setShowConfirmModal(false)}
                  >
                    <Text style={styles.modalCancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.modalConfirmButton}
                    onPress={confirmReschedule}
                  >
                    <Text style={styles.modalConfirmButtonText}>Confirm</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>

          {/* Success Modal */}
          <Modal
            visible={showSuccessModal}
            transparent={true}
            animationType="fade"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.successIcon}>✅</Text>
                <Text style={styles.modalTitle}>Appointment Rescheduled</Text>
                <Text style={styles.modalText}>
                  Your appointment has been successfully rescheduled to {selectedDate} at {selectedTime?.display || 'selected time'}.
                </Text>
                <TouchableOpacity
                  style={styles.modalDoneButton}
                  onPress={closeSuccessModal}
                >
                  <Text style={styles.modalDoneButtonText}>Done</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default RescheduleAppointment;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 50,
  },
  loadingIndicator: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F5F9EE',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#6B9142',
  },
  loadingEmoji: {
    fontSize: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B9142',
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 15,
    backgroundColor: '#6B9142',
    position: 'relative',
    zIndex: 10,
  },
  headerDecoration: {
    position: 'absolute',
    bottom: -20,
    left: 0,
    right: 0,
    height: 40,
    backgroundColor: '#6B9142',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    zIndex: -1,
  },
  backButton: {
    padding: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
    marginRight: 40,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  content: {
    flex: 1,
    padding: 20,
    paddingTop: 30,
    paddingBottom: 40,
  },
  appointmentCard: {
    backgroundColor: '#F5F9EE',
    borderRadius: 12,
    padding: 15,
    marginBottom: 20,
  },
  therapistInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  therapistImage: {
    fontSize: 30,
    marginRight: 10,
  },
  therapistName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  therapistSpecialty: {
    fontSize: 14,
    color: '#666666',
  },
  currentAppointmentText: {
    fontSize: 14,
    color: '#666666',
    fontStyle: 'italic',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 10,
    marginTop: 20,
  },
  datesContainer: {
    paddingBottom: 10,
  },
  dateCard: {
    width: 70,
    height: 90,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    padding: 10,
  },
  selectedDateCard: {
    backgroundColor: '#6B9142',
  },
  dateDay: {
    fontSize: 14,
    color: '#666666',
  },
  dateNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginVertical: 5,
  },
  dateMonth: {
    fontSize: 14,
    color: '#666666',
  },
  timeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  timeCard: {
    width: '30%',
    height: 50,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  selectedTimeCard: {
    backgroundColor: '#6B9142',
  },
  unavailableTimeCard: {
    backgroundColor: '#F5F5F5',
    opacity: 0.5,
  },
  timeText: {
    fontSize: 14,
    color: '#333333',
  },
  selectedTimeText: {
    color: '#FFFFFF',
  },
  unavailableTimeText: {
    color: '#999999',
  },
  bookedText: {
    fontSize: 12,
    color: '#999999',
  },
  rescheduleButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  rescheduleButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    width: '80%',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 10,
  },
  modalText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalCancelButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 12,
    width: '48%',
    alignItems: 'center',
  },
  modalCancelButtonText: {
    fontSize: 14,
    color: '#666666',
  },
  modalConfirmButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    padding: 12,
    width: '48%',
    alignItems: 'center',
  },
  modalConfirmButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  successIcon: {
    fontSize: 40,
    marginBottom: 10,
  },
  modalDoneButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    padding: 12,
    width: '100%',
    alignItems: 'center',
  },
  modalDoneButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});
