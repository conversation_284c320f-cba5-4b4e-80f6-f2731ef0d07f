import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Platform,
  StatusBar
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import CustomStatusBar from './components/CustomStatusBar';
import { useUser } from './context/UserContext';

const EditProfile = () => {
  const router = useRouter();
  const { userData } = useUser();
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  const personalInfo = [
    { label: 'First Name', value: userData.firstName || 'Lance' },
    { label: 'Middle Name', value: userData.middleName || 'N/A' },
    { label: 'Last Name', value: userData.lastName || 'Bautista' },
    { label: 'Age', value: userData.age || '22' },
    { label: 'Gender', value: userData.gender || 'Male' },
    { label: 'Civil Status', value: userData.civilStatus || 'Single' }
  ];

  const contactInfo = [
    { label: 'Email', value: userData.email || '<EMAIL>' },
    { label: 'Phone', value: userData.phone || '0123456789' },
    { label: 'Address', value: userData.address || '1234 San Isidro St. Sampaloc, Manila' }
  ];

  const backgroundInfo = [
    { label: 'Birthdate', value: userData.birthdate || '03-25-2005' },
    { label: 'Birthplace', value: userData.birthplace || 'Manila' },
    { label: 'Religion', value: userData.religion || 'Catholic' }
  ];

  const renderInfoSection = (title, data) => (
    <View style={styles.infoSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.sectionContent}>
        {data.map((item, index) => (
          <View 
            key={index} 
            style={[
              styles.infoItem,
              index === data.length - 1 && styles.lastInfoItem
            ]}
          >
            <Text style={styles.infoLabel}>{item.label}</Text>
            <Text style={styles.infoValue}>{item.value}</Text>
          </View>
        ))}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <CustomStatusBar backgroundColor="transparent" barStyle="light-content" />
      
      <SafeAreaView style={styles.safeArea}>
        {/* Header with Gradient */}
        <LinearGradient
          colors={['#7BA05B', '#9BC76D']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Text style={styles.backButtonIcon}>←</Text>
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Edit Profile</Text>
            <View style={styles.headerSpacer} />
          </View>
        </LinearGradient>

        <ScrollView 
          contentContainerStyle={styles.scrollContent} 
          showsVerticalScrollIndicator={false}
        >
          {/* Personal Information */}
          <Animated.View
            style={[
              styles.section,
              { opacity: fadeAnim }
            ]}
          >
            <Text style={styles.mainSectionTitle}>Personal Information</Text>
            
            {renderInfoSection('Basic Information', personalInfo)}
            {renderInfoSection('Contact Information', contactInfo)}
            {renderInfoSection('Background Information', backgroundInfo)}

            <TouchableOpacity 
              style={styles.editButton}
              onPress={() => {
                // Handle edit functionality
                console.log('Edit information pressed');
              }}
            >
              <Text style={styles.editButtonText}>Edit Information</Text>
            </TouchableOpacity>
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default EditProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  safeArea: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: Platform.OS === 'ios' ? 60 : StatusBar.currentHeight + 30,
    paddingBottom: 25,
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  backButtonIcon: {
    fontSize: 22,
    color: '#FFFFFF',
    marginRight: 6,
  },
  backButtonText: {
    fontSize: 17,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  headerSpacer: {
    width: 70,
  },
  scrollContent: {
    padding: 24,
    paddingBottom: 120,
  },
  section: {
    flex: 1,
  },
  mainSectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 28,
    letterSpacing: 0.5,
  },
  infoSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 18,
    letterSpacing: 0.3,
  },
  sectionContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F6FA',
  },
  lastInfoItem: {
    borderBottomWidth: 0,
  },
  infoLabel: {
    fontSize: 17,
    color: '#2C3E50',
    fontWeight: '600',
    flex: 1,
  },
  infoValue: {
    fontSize: 16,
    color: '#5D6D7E',
    textAlign: 'right',
    flex: 1,
    fontWeight: '500',
  },
  editButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 28,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 24,
    shadowColor: '#7BA05B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 6,
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
});
