import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
  Alert
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import BottomNavigation from './components/BottomNavigation';
import { useUser } from './context/UserContext';

const Inquiries = () => {
  const router = useRouter();
  const { userData } = useUser();
  const [activeTab, setActiveTab] = useState('new');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);

  const categories = [
    { id: 1, title: 'Services', icon: 'medical' },
    { id: 2, title: 'Appointments', icon: 'calendar' },
    { id: 3, title: 'Assessments', icon: 'clipboard' },
    { id: 4, title: 'Payments', icon: 'card' },
    { id: 5, title: 'Other', icon: 'help-circle' },
  ];

  const pastInquiries = [
    {
      id: 1,
      subject: 'Question about assessment results',
      date: '2023-05-10',
      status: 'answered',
      category: 'Assessments',
      message: 'I have a question about interpreting my DASS assessment results. Could you provide more information?',
      response: 'Thank you for your inquiry. The DASS assessment measures depression, anxiety, and stress levels. Your results indicate mild levels of anxiety. This is not a clinical diagnosis but can help guide your mental health journey. We recommend discussing these results with a professional for personalized advice.'
    },
    {
      id: 2,
      subject: 'Appointment rescheduling',
      date: '2023-04-25',
      status: 'answered',
      category: 'Appointments',
      message: 'I need to reschedule my appointment on April 30th. What are the available slots for the following week?',
      response: 'We have rescheduled your appointment to May 5th at 2:00 PM as requested. You will receive a confirmation email shortly. Please let us know if you need to make any further changes.'
    },
    {
      id: 3,
      subject: 'Payment method update',
      date: '2023-04-15',
      status: 'pending',
      category: 'Payments',
      message: 'I would like to update my payment method from credit card to debit card. How can I do this?',
      response: null
    },
  ];

  const [expandedInquiry, setExpandedInquiry] = useState(null);

  // Dismiss keyboard when tapping outside input fields
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const handleSubmit = () => {
    if (!selectedCategory) {
      Alert.alert('Error', 'Please select a category');
      return;
    }
    
    if (!subject.trim()) {
      Alert.alert('Error', 'Please enter a subject');
      return;
    }
    
    if (!message.trim()) {
      Alert.alert('Error', 'Please enter your message');
      return;
    }
    
    // Submit inquiry
    Alert.alert(
      'Inquiry Submitted',
      'Thank you for your inquiry. We will respond within 48 hours.',
      [{ text: 'OK', onPress: () => {
        setSelectedCategory(null);
        setSubject('');
        setMessage('');
      }}]
    );
  };

  const toggleInquiry = (id) => {
    if (expandedInquiry === id) {
      setExpandedInquiry(null);
    } else {
      setExpandedInquiry(id);
    }
  };

  const renderNewInquiry = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Submit a New Inquiry</Text>
      
      <Text style={styles.formLabel}>Select Category</Text>
      <View style={styles.categoriesContainer}>
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryButton,
              selectedCategory === category.id && styles.selectedCategoryButton
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <Ionicons
              name={category.icon}
              size={22}
              color={selectedCategory === category.id ? '#FFFFFF' : '#4CAF50'}
              style={styles.categoryIcon}
            />
            <Text
              style={[
                styles.categoryText,
                selectedCategory === category.id && styles.selectedCategoryText
              ]}
            >
              {category.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      
      <Text style={styles.formLabel}>Subject</Text>
      <TextInput
        style={styles.input}
        value={subject}
        onChangeText={setSubject}
        placeholder="Enter subject"
        placeholderTextColor="#999999"
      />
      
      <Text style={styles.formLabel}>Message</Text>
      <TextInput
        style={styles.messageInput}
        value={message}
        onChangeText={setMessage}
        placeholder="Type your inquiry here"
        placeholderTextColor="#999999"
        multiline
        textAlignVertical="top"
      />
      
      <TouchableOpacity
        style={styles.submitButton}
        onPress={handleSubmit}
      >
        <Text style={styles.submitButtonText}>Submit Inquiry</Text>
      </TouchableOpacity>
    </View>
  );

  const renderPastInquiries = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Past Inquiries</Text>
      
      {pastInquiries.length === 0 ? (
        <View style={styles.emptyState}>
          <Ionicons name="mail-open-outline" size={48} color="#CCCCCC" />
          <Text style={styles.emptyStateText}>You haven't submitted any inquiries yet</Text>
        </View>
      ) : (
        pastInquiries.map((inquiry) => (
          <TouchableOpacity
            key={inquiry.id}
            style={styles.inquiryItem}
            onPress={() => toggleInquiry(inquiry.id)}
          >
            <View style={styles.inquiryHeader}>
              <View>
                <Text style={styles.inquirySubject}>{inquiry.subject}</Text>
                <Text style={styles.inquiryMeta}>
                  {new Date(inquiry.date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                  })} • {inquiry.category}
                </Text>
              </View>
              <View style={styles.statusContainer}>
                <View style={[
                  styles.statusIndicator,
                  { backgroundColor: inquiry.status === 'answered' ? '#4CAF50' : '#FF9800' }
                ]} />
                <Text style={styles.statusText}>
                  {inquiry.status === 'answered' ? 'Answered' : 'Pending'}
                </Text>
              </View>
            </View>
            
            {expandedInquiry === inquiry.id && (
              <View style={styles.inquiryDetails}>
                <View style={styles.messageContainer}>
                  <Text style={styles.messageLabel}>Your Message:</Text>
                  <Text style={styles.messageText}>{inquiry.message}</Text>
                </View>
                
                {inquiry.response && (
                  <View style={styles.responseContainer}>
                    <Text style={styles.responseLabel}>Response:</Text>
                    <Text style={styles.responseText}>{inquiry.response}</Text>
                  </View>
                )}
              </View>
            )}
          </TouchableOpacity>
        ))
      )}
    </View>
  );

  return (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <SafeAreaView style={styles.container}>
        <StatusBar
          translucent={true}
          backgroundColor="transparent"
          barStyle="light-content"
        />
        
        {/* Header */}
        <LinearGradient
          colors={['#4CAF50', '#388E3C']}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Inquiries</Text>
            <View style={styles.placeholderButton} />
          </View>
        </LinearGradient>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'new' && styles.activeTab]}
            onPress={() => setActiveTab('new')}
          >
            <Text style={[styles.tabText, activeTab === 'new' && styles.activeTabText]}>
              New Inquiry
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'past' && styles.activeTab]}
            onPress={() => setActiveTab('past')}
          >
            <Text style={[styles.tabText, activeTab === 'past' && styles.activeTabText]}>
              Past Inquiries
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}
        >
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {activeTab === 'new' && renderNewInquiry()}
            {activeTab === 'past' && renderPastInquiries()}
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 15,
    paddingBottom: 15,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  placeholderButton: {
    width: 40,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingVertical: 5,
    paddingHorizontal: 10,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
  },
  tabText: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  tabContent: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    marginTop: 16,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedCategoryButton: {
    backgroundColor: '#4CAF50',
  },
  categoryIcon: {
    marginRight: 6,
  },
  categoryText: {
    fontSize: 13,
    color: '#4CAF50',
    fontWeight: '500',
  },
  selectedCategoryText: {
    color: '#FFFFFF',
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 15,
    color: '#333333',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  messageInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 15,
    color: '#333333',
    height: 150,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  submitButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 25,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 24,
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#999999',
    textAlign: 'center',
    marginTop: 16,
  },
  inquiryItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  inquiryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  inquirySubject: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  inquiryMeta: {
    fontSize: 12,
    color: '#999999',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  inquiryDetails: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  messageContainer: {
    marginBottom: 16,
  },
  messageLabel: {
    fontSize: 13,
    fontWeight: '600',
    color: '#666666',
    marginBottom: 4,
  },
  messageText: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
  responseContainer: {
    backgroundColor: 'rgba(76, 175, 80, 0.05)',
    borderRadius: 8,
    padding: 12,
  },
  responseLabel: {
    fontSize: 13,
    fontWeight: '600',
    color: '#4CAF50',
    marginBottom: 4,
  },
  responseText: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
});

export default Inquiries;
