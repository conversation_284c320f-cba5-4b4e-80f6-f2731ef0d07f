// This file is kept for compatibility
// It needs a default export to avoid warnings

// Add necessary polyfills for Node.js modules
import { <PERSON><PERSON><PERSON> } from 'buffer';
import EventEmitter from 'events';
import 'react-native-url-polyfill/auto';
import 'react-native-get-random-values';

// Add global polyfills
global.Buffer = Buffer;
global.EventEmitter = EventEmitter;
global.process = global.process || {};
global.process.env = global.process.env || {};
global.process.version = ''; // Mock Node.js version

// Completely disable WebSocket to prevent it from trying to use Node modules
// This is safe because our auth-service.js uses direct fetch API calls
global.WebSocket = null;

// Empty component to satisfy the router
import React from 'react';

export default function Polyfills() {
  return null;
}





