// Supabase configuration and client setup
export const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://aohjojhuigksropdoydc.supabase.co';
export const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvaGpvamh1aWdrc3JvcGRveWRjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzOTEwMjMsImV4cCI6MjA2MTk2NzAyM30.TDKS6-s4eYQ0Y1ITKFaM6ljU_LlUG2AAXVJDj7y6K6A';

// Simple Supabase client using fetch API for appointments and schedules
export const supabase = {
  // Fetch data from a table
  from: (table) => ({
    select: (columns = '*') => ({
      eq: (column, value) => ({
        order: (orderColumn, options = {}) =>
          fetchFromSupabase(table, columns, { [column]: value }, orderColumn, options),
        execute: () =>
          fetchFromSupabase(table, columns, { [column]: value })
      }),
      execute: () =>
        fetchFromSupabase(table, columns),
      order: (orderColumn, options = {}) =>
        fetchFromSupabase(table, columns, null, orderColumn, options)
    }),
    insert: (data) => ({
      select: () => insertToSupabase(table, data, true),
      execute: () => insertToSupabase(table, data, false)
    }),
    update: (data) => ({
      eq: (column, value) => ({
        execute: () => updateInSupabase(table, data, { [column]: value })
      })
    }),
    delete: () => ({
      eq: (column, value) => ({
        execute: () => deleteFromSupabase(table, { [column]: value })
      })
    })
  })
};

// Helper function to fetch data from Supabase
const fetchFromSupabase = async (table, columns, filters = null, orderColumn = null, orderOptions = {}) => {
  try {
    let url = `${supabaseUrl}/rest/v1/${table}`;

    // Add select parameter
    if (columns !== '*') {
      url += `?select=${columns}`;
    }

    // Add filters
    if (filters) {
      const filterParams = Object.entries(filters)
        .map(([key, value]) => `${key}=eq.${value}`)
        .join('&');
      url += columns !== '*' ? `&${filterParams}` : `?${filterParams}`;
    }

    // Add ordering
    if (orderColumn) {
      const orderParam = `order=${orderColumn}.${orderOptions.ascending === false ? 'desc' : 'asc'}`;
      url += (url.includes('?') ? '&' : '?') + orderParam;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching from Supabase:', error);
    return { data: null, error };
  }
};

// Helper function to insert data to Supabase
const insertToSupabase = async (table, data, returnData = false) => {
  try {
    console.log('Inserting to Supabase:', { table, data });

    const response = await fetch(`${supabaseUrl}/rest/v1/${table}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Prefer': returnData ? 'return=representation' : 'return=minimal'
      },
      body: JSON.stringify(Array.isArray(data) ? data : [data]),
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const responseData = returnData ? await response.json() : null;
    console.log('Insert successful:', responseData);
    return { data: responseData, error: null };
  } catch (error) {
    console.error('Error inserting to Supabase:', error);
    return { data: null, error };
  }
};

// Helper function to update data in Supabase
const updateInSupabase = async (table, data, filters) => {
  try {
    const filterParams = Object.entries(filters)
      .map(([key, value]) => `${key}=eq.${value}`)
      .join('&');

    const response = await fetch(`${supabaseUrl}/rest/v1/${table}?${filterParams}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return { data: null, error: null };
  } catch (error) {
    console.error('Error updating in Supabase:', error);
    return { data: null, error };
  }
};

// Helper function to delete data from Supabase
const deleteFromSupabase = async (table, filters) => {
  try {
    const filterParams = Object.entries(filters)
      .map(([key, value]) => `${key}=eq.${value}`)
      .join('&');

    const response = await fetch(`${supabaseUrl}/rest/v1/${table}?${filterParams}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return { data: null, error: null };
  } catch (error) {
    console.error('Error deleting from Supabase:', error);
    return { data: null, error };
  }
};

export default supabase;



