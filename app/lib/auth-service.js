// Simple user table operations with Supabase OTP email verification
import { supabaseUrl, supabase<PERSON>nonKey } from './supabase';

export const signUp = async (email, password) => {
  try {
    console.log('Creating user in user table:', email);

    // Check if user already exists
    const checkResponse = await fetch(`${supabaseUrl}/rest/v1/user?email=eq.${email}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
      },
    });

    const existingUsers = await checkResponse.json();

    if (existingUsers && existingUsers.length > 0) {
      throw new Error('User with this email already exists');
    }

    // Create new user in user table
    const signUpResponse = await fetch(`${supabaseUrl}/rest/v1/user`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Prefer': 'return=representation'
      },
      body: JSON.stringify({
        email,
        password,
        email_verified: false,
        has_completed_profile: false
      }),
    });

    const data = await signUpResponse.json();

    if (!signUpResponse.ok) {
      throw new Error(data.message || 'Failed to create user');
    }

    console.log('User created in table:', data);

    // Send verification email using Supabase's built-in OTP system
    try {
      const otpResponse = await fetch(`${supabaseUrl}/auth/v1/otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
        },
        body: JSON.stringify({
          email: email,
          type: 'email'
        }),
      });

      if (otpResponse.ok) {
        console.log('✅ Verification email sent via Supabase to:', email);
        console.log('📧 User should check their email for the 6-digit verification code');
      } else {
        console.log('❌ Failed to send Supabase OTP email');
      }
    } catch (emailError) {
      console.log('❌ Supabase email error:', emailError);
    }

    return { data: data[0], error: null };
  } catch (error) {
    console.error('Create user error:', error);
    return { data: null, error };
  }
};

export const signIn = async (email, password) => {
  try {
    console.log('Finding user in user table:', email);

    // Find user in user table
    const response = await fetch(`${supabaseUrl}/rest/v1/user?email=eq.${email}&password=eq.${password}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error('Failed to find user');
    }

    if (!data || data.length === 0) {
      throw new Error('Invalid email or password');
    }

    const user = data[0];
    console.log('User found in table:', user);

    return { data: { user }, error: null };
  } catch (error) {
    console.error('Find user error:', error);
    return { data: null, error };
  }
};

export const signOut = async () => {
  try {
    console.log('Signing out user');

    // Since we're using simple table-based auth,
    // signOut just clears local data (handled by the app)
    return { error: null };
  } catch (error) {
    console.error('Sign out error:', error);
    return { error };
  }
};

export default { signUp, signIn, signOut };

