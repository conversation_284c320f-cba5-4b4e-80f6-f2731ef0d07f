import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Image,
  Platform,
  TextInput,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import BottomNavigation from './components/BottomNavigation';
import { useUser } from './context/UserContext';

const PaymentDetails = () => {
  const router = useRouter();
  const { userData } = useUser();
  const [activeTab, setActiveTab] = useState('payment-methods');
  const [paymentMethods, setPaymentMethods] = useState([
    { id: 1, type: 'credit', last4: '4242', expiry: '04/25', default: true },
    { id: 2, type: 'debit', last4: '1234', expiry: '12/24', default: false },
  ]);
  const [transactions, setTransactions] = useState([
    { id: 1, date: '2023-05-15', amount: 1500, description: 'Consultation Fee', status: 'completed' },
    { id: 2, date: '2023-04-20', amount: 500, description: 'Assessment Fee', status: 'completed' },
    { id: 3, date: '2023-03-10', amount: 1500, description: 'Consultation Fee', status: 'completed' },
  ]);
  const [receipts, setReceipts] = useState([
    { id: 1, date: '2023-05-15', amount: 1500, description: 'Consultation Fee', receiptNo: 'REC-001-2023' },
    { id: 2, date: '2023-04-20', amount: 500, description: 'Assessment Fee', receiptNo: 'REC-002-2023' },
    { id: 3, date: '2023-03-10', amount: 1500, description: 'Consultation Fee', receiptNo: 'REC-003-2023' },
  ]);

  // Dismiss keyboard when tapping outside input fields
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const renderPaymentMethods = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Your Payment Methods</Text>
      {paymentMethods.map((method) => (
        <View key={method.id} style={styles.paymentMethodCard}>
          <View style={styles.cardIconContainer}>
            {method.type === 'credit' ? (
              <Ionicons name="card" size={24} color="#00BCD4" />
            ) : (
              <Ionicons name="wallet" size={24} color="#00BCD4" />
            )}
          </View>
          <View style={styles.cardDetails}>
            <Text style={styles.cardType}>
              {method.type === 'credit' ? 'Credit Card' : 'Debit Card'} 
              {method.default && <Text style={styles.defaultBadge}> (Default)</Text>}
            </Text>
            <Text style={styles.cardNumber}>**** **** **** {method.last4}</Text>
            <Text style={styles.cardExpiry}>Expires: {method.expiry}</Text>
          </View>
          <TouchableOpacity style={styles.editButton}>
            <Ionicons name="create-outline" size={20} color="#00BCD4" />
          </TouchableOpacity>
        </View>
      ))}
      <TouchableOpacity style={styles.addButton}>
        <Ionicons name="add-circle" size={20} color="#FFFFFF" style={styles.addIcon} />
        <Text style={styles.addButtonText}>Add Payment Method</Text>
      </TouchableOpacity>
    </View>
  );

  const renderTransactions = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Transaction History</Text>
      {transactions.map((transaction) => (
        <View key={transaction.id} style={styles.transactionCard}>
          <View style={styles.transactionHeader}>
            <Text style={styles.transactionDate}>
              {new Date(transaction.date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
              })}
            </Text>
            <Text style={styles.transactionAmount}>₱{transaction.amount.toFixed(2)}</Text>
          </View>
          <Text style={styles.transactionDescription}>{transaction.description}</Text>
          <View style={styles.transactionStatus}>
            <View style={[
              styles.statusIndicator,
              { backgroundColor: transaction.status === 'completed' ? '#4CAF50' : '#FF9800' }
            ]} />
            <Text style={styles.statusText}>
              {transaction.status === 'completed' ? 'Completed' : 'Pending'}
            </Text>
          </View>
        </View>
      ))}
    </View>
  );

  const renderReceipts = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Receipts & Invoices</Text>
      {receipts.map((receipt) => (
        <View key={receipt.id} style={styles.receiptCard}>
          <View style={styles.receiptHeader}>
            <Text style={styles.receiptDate}>
              {new Date(receipt.date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
              })}
            </Text>
            <Text style={styles.receiptNo}>{receipt.receiptNo}</Text>
          </View>
          <Text style={styles.receiptDescription}>{receipt.description}</Text>
          <Text style={styles.receiptAmount}>₱{receipt.amount.toFixed(2)}</Text>
          <TouchableOpacity style={styles.downloadButton}>
            <Ionicons name="download-outline" size={16} color="#FFFFFF" style={styles.downloadIcon} />
            <Text style={styles.downloadText}>Download</Text>
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );

  return (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <SafeAreaView style={styles.container}>
        <StatusBar
          translucent={true}
          backgroundColor="transparent"
          barStyle="light-content"
        />
        
        {/* Header */}
        <LinearGradient
          colors={['#00BCD4', '#00ACC1']}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Payment Details</Text>
            <View style={styles.placeholderButton} />
          </View>
        </LinearGradient>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'payment-methods' && styles.activeTab]}
            onPress={() => setActiveTab('payment-methods')}
          >
            <Text style={[styles.tabText, activeTab === 'payment-methods' && styles.activeTabText]}>
              Payment Methods
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'transactions' && styles.activeTab]}
            onPress={() => setActiveTab('transactions')}
          >
            <Text style={[styles.tabText, activeTab === 'transactions' && styles.activeTabText]}>
              Transactions
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'receipts' && styles.activeTab]}
            onPress={() => setActiveTab('receipts')}
          >
            <Text style={[styles.tabText, activeTab === 'receipts' && styles.activeTabText]}>
              Receipts
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}
        >
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {activeTab === 'payment-methods' && renderPaymentMethods()}
            {activeTab === 'transactions' && renderTransactions()}
            {activeTab === 'receipts' && renderReceipts()}
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 15,
    paddingBottom: 15,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  placeholderButton: {
    width: 40,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingVertical: 5,
    paddingHorizontal: 10,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: 'rgba(0, 188, 212, 0.1)',
  },
  tabText: {
    fontSize: 13,
    color: '#666666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#00BCD4',
    fontWeight: 'bold',
  },
  tabContent: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 16,
  },
  paymentMethodCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 188, 212, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cardDetails: {
    flex: 1,
  },
  cardType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  defaultBadge: {
    color: '#00BCD4',
    fontWeight: 'bold',
  },
  cardNumber: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  cardExpiry: {
    fontSize: 12,
    color: '#999999',
  },
  editButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 188, 212, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButton: {
    backgroundColor: '#00BCD4',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    shadowColor: '#00BCD4',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  addIcon: {
    marginRight: 8,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  transactionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  transactionDate: {
    fontSize: 14,
    color: '#666666',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  transactionDescription: {
    fontSize: 15,
    color: '#333333',
    marginBottom: 8,
  },
  transactionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: '#666666',
  },
  receiptCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  receiptHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  receiptDate: {
    fontSize: 14,
    color: '#666666',
  },
  receiptNo: {
    fontSize: 12,
    color: '#999999',
  },
  receiptDescription: {
    fontSize: 15,
    color: '#333333',
    marginBottom: 4,
  },
  receiptAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
  },
  downloadButton: {
    backgroundColor: '#00BCD4',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start',
  },
  downloadIcon: {
    marginRight: 6,
  },
  downloadText: {
    color: '#FFFFFF',
    fontSize: 13,
    fontWeight: '600',
  },
});

export default PaymentDetails;
