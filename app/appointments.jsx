import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  Modal,
  Platform,
  Alert
} from 'react-native';
import { useState } from 'react';
import { useRouter } from 'expo-router';
import { useAppointment } from './context/AppointmentContext';
import CustomStatusBar from './components/CustomStatusBar';
import BottomNavigation from './components/BottomNavigation';

const Appointments = () => {
  const router = useRouter();
  const {
    getUpcomingAppointments,
    getPastAppointments,
    cancelAppointment
  } = useAppointment();

  const [activeTab, setActiveTab] = useState('upcoming');
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);

  const upcomingAppointments = getUpcomingAppointments();
  const pastAppointments = getPastAppointments();

  const handleAppointmentPress = (appointment) => {
    setSelectedAppointment(appointment);
    setShowDetailsModal(true);
  };

  const handleCancelAppointment = () => {
    setShowCancelModal(false);
    setShowDetailsModal(false);

    try {
      cancelAppointment(selectedAppointment.id);
      Alert.alert(
        "Appointment Cancelled",
        "Your appointment has been successfully cancelled."
      );
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      Alert.alert(
        "Error",
        "There was an error cancelling your appointment. Please try again."
      );
    }
  };

  const formatAppointmentDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderAppointmentList = () => {
    const appointments = activeTab === 'upcoming' ? upcomingAppointments : pastAppointments;

    if (appointments.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateIcon}>📅</Text>
          <Text style={styles.emptyStateTitle}>
            No {activeTab === 'upcoming' ? 'upcoming' : 'past'} appointments
          </Text>
          <Text style={styles.emptyStateText}>
            {activeTab === 'upcoming'
              ? 'Schedule your first appointment with a therapist'
              : 'Your past appointments will appear here'
            }
          </Text>
          {activeTab === 'upcoming' && (
            <TouchableOpacity
              style={styles.scheduleButton}
              onPress={() => router.replace('/schedule-appointment')}
            >
              <Text style={styles.scheduleButtonText}>Schedule Appointment</Text>
            </TouchableOpacity>
          )}
        </View>
      );
    }

    return (
      <View style={styles.appointmentsList}>
        {appointments.map((appointment) => (
          <TouchableOpacity
            key={appointment.id}
            style={styles.appointmentCard}
            onPress={() => handleAppointmentPress(appointment)}
          >
            <View style={styles.appointmentHeader}>
              <View style={styles.therapistInfo}>
                <Text style={styles.therapistImage}>{appointment.psychometrician?.image || '👨‍⚕️'}</Text>
                <View>
                  <Text style={styles.therapistName}>{appointment.psychometrician?.name || 'Psychometrician'}</Text>
                  <Text style={styles.therapistSpecialty}>{appointment.psychometrician?.specialty || 'Mental Health Professional'}</Text>
                </View>
              </View>
              <View style={[
                styles.statusBadge,
                appointment.status === 'scheduled' ? styles.scheduledBadge :
                appointment.status === 'completed' ? styles.completedBadge :
                styles.cancelledBadge
              ]}>
                <Text style={styles.statusText}>
                  {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                </Text>
              </View>
            </View>

            <View style={styles.appointmentDetails}>
              <View style={styles.detailItem}>
                <Text style={styles.detailIcon}>📅</Text>
                <Text style={styles.detailText}>{formatAppointmentDate(appointment.date)}</Text>
              </View>
              <View style={styles.detailItem}>
                <Text style={styles.detailIcon}>⏰</Text>
                <Text style={styles.detailText}>{appointment.start_time} - {appointment.end_time}</Text>
              </View>
              {appointment.notes && (
                <View style={styles.detailItem}>
                  <Text style={styles.detailIcon}>📝</Text>
                  <Text style={styles.detailText} numberOfLines={1}>{appointment.notes}</Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <CustomStatusBar backgroundColor="transparent" barStyle="dark-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Appointments</Text>
        {activeTab === 'upcoming' && (
          <TouchableOpacity
            style={styles.scheduleHeaderButton}
            onPress={() => router.replace('/schedule-appointment')}
          >
            <Text style={styles.scheduleHeaderButtonText}>+ New</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'upcoming' && styles.activeTabButton]}
          onPress={() => setActiveTab('upcoming')}
        >
          <Text style={[styles.tabText, activeTab === 'upcoming' && styles.activeTabText]}>
            Upcoming
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'past' && styles.activeTabButton]}
          onPress={() => setActiveTab('past')}
        >
          <Text style={[styles.tabText, activeTab === 'past' && styles.activeTabText]}>
            Past
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView contentContainerStyle={styles.content}>
        {renderAppointmentList()}
      </ScrollView>

      {/* Appointment Details Modal */}
      <Modal
        visible={showDetailsModal}
        transparent={true}
        animationType="none"
      >
        {selectedAppointment && (
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Appointment Details</Text>
                <TouchableOpacity
                  onPress={() => setShowDetailsModal(false)}
                  style={styles.closeButton}
                >
                  <Text style={styles.closeButtonText}>✕</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.modalTherapistInfo}>
                <Text style={styles.modalTherapistImage}>{selectedAppointment.psychometrician?.image || '👨‍⚕️'}</Text>
                <View>
                  <Text style={styles.modalTherapistName}>{selectedAppointment.psychometrician?.name || 'Psychometrician'}</Text>
                  <Text style={styles.modalTherapistSpecialty}>{selectedAppointment.psychometrician?.specialty || 'Mental Health Professional'}</Text>
                  <Text style={styles.modalTherapistExperience}>{selectedAppointment.psychometrician?.experience || '5+ years'} • ⭐ {selectedAppointment.psychometrician?.rating || '4.8'}</Text>
                </View>
              </View>

              <View style={styles.modalAppointmentDetails}>
                <View style={styles.modalDetailItem}>
                  <Text style={styles.modalDetailLabel}>Date:</Text>
                  <Text style={styles.modalDetailValue}>{formatAppointmentDate(selectedAppointment.date)}</Text>
                </View>
                <View style={styles.modalDetailItem}>
                  <Text style={styles.modalDetailLabel}>Time:</Text>
                  <Text style={styles.modalDetailValue}>{selectedAppointment.start_time} - {selectedAppointment.end_time}</Text>
                </View>
                <View style={styles.modalDetailItem}>
                  <Text style={styles.modalDetailLabel}>Status:</Text>
                  <View style={[
                    styles.modalStatusBadge,
                    selectedAppointment.status === 'scheduled' ? styles.scheduledBadge :
                    selectedAppointment.status === 'completed' ? styles.completedBadge :
                    styles.cancelledBadge
                  ]}>
                    <Text style={styles.modalStatusText}>
                      {selectedAppointment.status.charAt(0).toUpperCase() + selectedAppointment.status.slice(1)}
                    </Text>
                  </View>
                </View>
                {selectedAppointment.notes && (
                  <View style={styles.modalDetailItem}>
                    <Text style={styles.modalDetailLabel}>Notes:</Text>
                    <Text style={styles.modalDetailValue}>{selectedAppointment.notes}</Text>
                  </View>
                )}
              </View>

              {/* Previous Assessment Section */}
              {selectedAppointment.status === 'completed' && (
                <View style={styles.assessmentSection}>
                  <Text style={styles.assessmentSectionTitle}>Previous Assessment</Text>
                  <View style={styles.assessmentCard}>
                    <View style={styles.assessmentHeader}>
                      <Text style={styles.assessmentDate}>
                        Completed on {formatAppointmentDate(selectedAppointment.date)}
                      </Text>
                      <View style={styles.assessmentScoreBadge}>
                        <Text style={styles.assessmentScoreText}>Score: 72/100</Text>
                      </View>
                    </View>

                    <View style={styles.assessmentResultsContainer}>
                      <View style={styles.assessmentResultItem}>
                        <Text style={styles.assessmentResultLabel}>Anxiety:</Text>
                        <View style={styles.assessmentResultBar}>
                          <View style={[styles.assessmentResultFill, { width: '45%', backgroundColor: '#FFC107' }]} />
                        </View>
                        <Text style={styles.assessmentResultValue}>Moderate</Text>
                      </View>

                      <View style={styles.assessmentResultItem}>
                        <Text style={styles.assessmentResultLabel}>Depression:</Text>
                        <View style={styles.assessmentResultBar}>
                          <View style={[styles.assessmentResultFill, { width: '30%', backgroundColor: '#4CAF50' }]} />
                        </View>
                        <Text style={styles.assessmentResultValue}>Mild</Text>
                      </View>

                      <View style={styles.assessmentResultItem}>
                        <Text style={styles.assessmentResultLabel}>Stress:</Text>
                        <View style={styles.assessmentResultBar}>
                          <View style={[styles.assessmentResultFill, { width: '60%', backgroundColor: '#FF5722' }]} />
                        </View>
                        <Text style={styles.assessmentResultValue}>High</Text>
                      </View>
                    </View>

                    <TouchableOpacity
                      style={styles.viewFullAssessmentButton}
                      onPress={() => {
                        setShowDetailsModal(false);
                        router.replace('/assessment-results');
                      }}
                    >
                      <Text style={styles.viewFullAssessmentButtonText}>View Full Assessment</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}

              {selectedAppointment.status === 'scheduled' && (
                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={styles.rescheduleButton}
                    onPress={() => {
                      setShowDetailsModal(false);
                      router.replace({
                        pathname: '/reschedule-appointment',
                        params: { id: selectedAppointment.id }
                      });
                    }}
                  >
                    <Text style={styles.rescheduleButtonText}>Reschedule</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.cancelAppointmentButton}
                    onPress={() => {
                      setShowCancelModal(true);
                    }}
                  >
                    <Text style={styles.cancelAppointmentButtonText}>Cancel Appointment</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        )}
      </Modal>

      {/* Cancel Confirmation Modal */}
      <Modal
        visible={showCancelModal}
        transparent={true}
        animationType="none"
      >
        <View style={styles.modalOverlay}>
          <View style={styles.confirmModalContent}>
            <Text style={styles.confirmModalTitle}>Cancel Appointment?</Text>
            <Text style={styles.confirmModalText}>
              Are you sure you want to cancel this appointment? This action cannot be undone.
            </Text>
            <View style={styles.confirmModalButtons}>
              <TouchableOpacity
                style={styles.confirmModalCancelButton}
                onPress={() => setShowCancelModal(false)}
              >
                <Text style={styles.confirmModalCancelButtonText}>No, Keep It</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.confirmModalConfirmButton}
                onPress={handleCancelAppointment}
              >
                <Text style={styles.confirmModalConfirmButtonText}>Yes, Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      <BottomNavigation />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 10 : 10,
    paddingBottom: 10,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
  },
  scheduleHeaderButton: {
    backgroundColor: '#6B9142',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  scheduleHeaderButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 14,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  tabButton: {
    paddingVertical: 12,
    marginRight: 24,
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#6B9142',
  },
  tabText: {
    fontSize: 16,
    color: '#666666',
  },
  activeTabText: {
    color: '#6B9142',
    fontWeight: 'bold',
  },
  content: {
    padding: 16,
    paddingBottom: 80, // Space for bottom navigation
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateIcon: {
    fontSize: 50,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 24,
  },
  scheduleButton: {
    backgroundColor: '#6B9142',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
  },
  scheduleButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  appointmentsList: {
    marginBottom: 20,
  },
  appointmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  therapistInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  therapistImage: {
    fontSize: 30,
    marginRight: 12,
  },
  therapistName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 2,
  },
  therapistSpecialty: {
    fontSize: 12,
    color: '#666666',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  scheduledBadge: {
    backgroundColor: 'rgba(107, 145, 66, 0.1)',
  },
  completedBadge: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  cancelledBadge: {
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333333',
  },
  appointmentDetails: {
    backgroundColor: '#F9F9F9',
    borderRadius: 8,
    padding: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#333333',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 340,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
  },
  closeButton: {
    padding: 4,
  },
  closeButtonText: {
    fontSize: 20,
    color: '#666666',
  },
  modalTherapistInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTherapistImage: {
    fontSize: 40,
    marginRight: 15,
  },
  modalTherapistName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 2,
  },
  modalTherapistSpecialty: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  modalTherapistExperience: {
    fontSize: 12,
    color: '#888888',
  },
  modalAppointmentDetails: {
    backgroundColor: '#F9F9F9',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  modalDetailItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  modalDetailLabel: {
    width: 60,
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
  },
  modalDetailValue: {
    flex: 1,
    fontSize: 14,
    color: '#333333',
  },
  modalStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  modalStatusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333333',
  },
  modalActions: {
    marginTop: 10,
  },
  rescheduleButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  rescheduleButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelAppointmentButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cancelAppointmentButtonText: {
    color: '#FF3B30',
    fontSize: 16,
    fontWeight: '600',
  },
  confirmModalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 340,
  },
  confirmModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
    textAlign: 'center',
  },
  confirmModalText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 20,
  },
  confirmModalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  confirmModalCancelButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flex: 1,
    marginRight: 8,
    alignItems: 'center',
  },
  confirmModalCancelButtonText: {
    color: '#666666',
    fontSize: 14,
    fontWeight: '600',
  },
  confirmModalConfirmButton: {
    backgroundColor: '#FF3B30',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flex: 1,
    marginLeft: 8,
    alignItems: 'center',
  },
  confirmModalConfirmButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  // Assessment styles
  assessmentSection: {
    width: '100%',
    marginBottom: 20,
  },
  assessmentSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
  },
  assessmentCard: {
    backgroundColor: '#F5F9EE',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 145, 66, 0.3)',
  },
  assessmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  assessmentDate: {
    fontSize: 12,
    color: '#666666',
  },
  assessmentScoreBadge: {
    backgroundColor: 'rgba(107, 145, 66, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  assessmentScoreText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  assessmentResultsContainer: {
    marginBottom: 16,
  },
  assessmentResultItem: {
    marginBottom: 12,
  },
  assessmentResultLabel: {
    fontSize: 13,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  assessmentResultBar: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginBottom: 4,
    overflow: 'hidden',
  },
  assessmentResultFill: {
    height: '100%',
    borderRadius: 4,
  },
  assessmentResultValue: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'right',
  },
  viewFullAssessmentButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    paddingVertical: 10,
    alignItems: 'center',
  },
  viewFullAssessmentButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default Appointments;
