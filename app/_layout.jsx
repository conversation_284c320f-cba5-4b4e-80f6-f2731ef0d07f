// Import polyfills first
import './polyfills';

import { Stack } from 'expo-router';
import { useCallback } from 'react';
import { View, StatusBar } from 'react-native';
import { useFonts } from 'expo-font';
import { UserProvider } from './context/UserContext';
import { AppointmentProvider } from './context/AppointmentContext';

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    // You can add custom fonts here if needed
    // 'CustomFont-Regular': require('../assets/fonts/CustomFont-Regular.ttf'),
  });

  // Simplified onLayoutRootView function without SplashScreen
  const onLayoutRootView = useCallback(() => {
    // No need to do anything here now
  }, [fontsLoaded]);

  if (!fontsLoaded) {
    return null;
  }

  return (
    <UserProvider>
      <AppointmentProvider>
        <View style={{ flex: 1 }} onLayout={onLayoutRootView}>
          <StatusBar
            barStyle="dark-content"
            backgroundColor="transparent"
            translucent={true}
          />
          <Stack
            screenOptions={{
              headerShown: false,
              contentStyle: {
                backgroundColor: '#ffffff',
              },
              // Disable animations between screens for instant navigation
              animation: 'none',
              animationDuration: 0,
              // Prevent gesture navigation for specific screens
              gestureEnabled: false,
              // Simple presentation style
              presentation: 'transparentModal',
            }}
          >
            {/* Authentication Flow */}
            <Stack.Screen name="index" />
            <Stack.Screen name="app-features" />
            <Stack.Screen name="welcome" />
            <Stack.Screen name="consent" />
            <Stack.Screen name="sign-in" />
            <Stack.Screen name="create-account" />
            <Stack.Screen name="email-verification" />
            <Stack.Screen name="personal-information" />

            {/* Main App Screens */}
            <Stack.Screen
              name="dashboard"
              options={{
                gestureEnabled: false, // Prevent swiping back from dashboard
              }}
            />

            {/* Mood & Mental Health */}
            <Stack.Screen name="mood-tracker" />
            <Stack.Screen name="mood-journal" />
            <Stack.Screen name="mental-assessment" />
            <Stack.Screen name="assessment-questions" />
            <Stack.Screen name="assessment-results" />

            {/* AI & Consultation */}
            <Stack.Screen name="ai-chatbot" />
            <Stack.Screen name="online-consultation" />

            {/* User & Support */}
            <Stack.Screen name="user-profile" />
            <Stack.Screen name="payment-details" />
            <Stack.Screen name="customer-support" />
            <Stack.Screen name="inquiries" />

            {/* Appointments */}
            <Stack.Screen name="appointments" />
            <Stack.Screen name="schedule-appointment" />
            <Stack.Screen name="reschedule-appointment" />
          </Stack>
        </View>
      </AppointmentProvider>
    </UserProvider>
  );
}






