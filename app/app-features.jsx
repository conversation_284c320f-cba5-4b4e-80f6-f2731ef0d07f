import React, { useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Animated,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

const AppFeatures = () => {
  const router = useRouter();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const features = [
    {
      icon: '📊',
      title: 'Mood tracker',
      color: '#6B9142',
    },
    {
      icon: '💬',
      title: 'AI chatbot',
      color: '#5A7C32',
    },
    {
      icon: '💎',
      title: 'Stress Management',
      color: '#7BA055',
    },
    {
      icon: '📋',
      title: 'Mental Health\nAssessment',
      color: '#4A6B2A',
    },
    {
      icon: '📅',
      title: 'Appointment',
      color: '#8FB069',
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* Logo */}
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/main.jpg')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.logoText}>mentalease</Text>
        </View>

        {/* App Features Title */}
        <Text style={styles.title}>App features</Text>

        {/* Features Grid */}
        <View style={styles.featuresContainer}>
          <View style={styles.featuresRow}>
            {features.slice(0, 3).map((feature, index) => (
              <Animated.View
                key={index}
                style={[
                  styles.featureItem,
                  {
                    opacity: fadeAnim,
                    transform: [
                      {
                        translateY: Animated.add(
                          slideAnim,
                          new Animated.Value(index * 10)
                        ),
                      },
                    ],
                  },
                ]}
              >
                <View style={[styles.featureIcon, { backgroundColor: feature.color }]}>
                  <Text style={styles.iconText}>{feature.icon}</Text>
                </View>
                <Text style={styles.featureTitle}>{feature.title}</Text>
              </Animated.View>
            ))}
          </View>
          
          <View style={styles.featuresRow}>
            {features.slice(3, 5).map((feature, index) => (
              <Animated.View
                key={index + 3}
                style={[
                  styles.featureItem,
                  {
                    opacity: fadeAnim,
                    transform: [
                      {
                        translateY: Animated.add(
                          slideAnim,
                          new Animated.Value((index + 3) * 10)
                        ),
                      },
                    ],
                  },
                ]}
              >
                <View style={[styles.featureIcon, { backgroundColor: feature.color }]}>
                  <Text style={styles.iconText}>{feature.icon}</Text>
                </View>
                <Text style={styles.featureTitle}>{feature.title}</Text>
              </Animated.View>
            ))}
          </View>
        </View>

        {/* Continue Button */}
        <Animated.View
          style={[
            styles.buttonContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <TouchableOpacity
            style={styles.continueButton}
            onPress={() => router.push('/welcome')}
          >
            <Text style={styles.continueButtonText}>Continue</Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Partnership Text */}
        <Text style={styles.partnershipText}>
          in Partnership with Sanda Diagnostic Center
        </Text>
      </Animated.View>
    </SafeAreaView>
  );
};

export default AppFeatures;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8E8E8',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 8,
  },
  logoText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2D5016',
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#2D5016',
    marginBottom: 40,
  },
  featuresContainer: {
    flex: 1,
    justifyContent: 'center',
    width: '100%',
  },
  featuresRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 40,
  },
  featureItem: {
    alignItems: 'center',
    width: width * 0.25,
  },
  featureIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconText: {
    fontSize: 24,
  },
  featureTitle: {
    fontSize: 12,
    color: '#2D5016',
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 16,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 20,
  },
  continueButton: {
    backgroundColor: '#2D5016',
    borderRadius: 25,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  partnershipText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
  },
});
