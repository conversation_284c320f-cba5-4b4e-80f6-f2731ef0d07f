import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Animated
} from 'react-native';
import { useRouter } from 'expo-router';

const Consent = () => {
  const router = useRouter();
  const [termsChecked, setTermsChecked] = useState(false);
  const [dataProcessingChecked, setDataProcessingChecked] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleContinue = () => {
    if (termsChecked && dataProcessingChecked) {
      router.push('/create-account');
    } else {
      // You could add an alert or visual feedback here
      console.log('Please agree to all terms');
    }
  };

  const handleAcceptAll = () => {
    setTermsChecked(true);
    setDataProcessingChecked(true);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Back Button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
      </View>

      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* Logo */}
        <View style={styles.logoContainer}>
          <View style={styles.shieldContainer}>
            <Image
              source={require('../assets/main.jpg')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>
          <Text style={styles.logoText}>mentalease</Text>
        </View>

        {/* Title */}
        <Text style={styles.title}>Your body. Your Data</Text>

        {/* Description */}
        <Text style={styles.description}>
          Your health data will never be shared with any
          company but Sanda Diagnostic Center.
        </Text>

        {/* Checkboxes */}
        <View style={styles.checkboxSection}>
          <View style={styles.checkboxContainer}>
            <TouchableOpacity
              style={[styles.checkbox, termsChecked && styles.checkboxChecked]}
              onPress={() => setTermsChecked(!termsChecked)}
              activeOpacity={0.7}
            >
              {termsChecked && <Text style={styles.checkmark}>✓</Text>}
            </TouchableOpacity>
            <Text style={styles.checkboxLabel}>
              I agree to MentalEase's terms & conditions
            </Text>
          </View>

          <View style={styles.checkboxContainer}>
            <TouchableOpacity
              style={[styles.checkbox, dataProcessingChecked && styles.checkboxChecked]}
              onPress={() => setDataProcessingChecked(!dataProcessingChecked)}
              activeOpacity={0.7}
            >
              {dataProcessingChecked && <Text style={styles.checkmark}>✓</Text>}
            </TouchableOpacity>
            <Text style={styles.checkboxLabel}>
              I agree to the processing of my personal & health
              data by MentalEase for the intended use of the app
            </Text>
          </View>
        </View>

        {/* Accept All Button */}
        <TouchableOpacity
          style={styles.acceptAllButton}
          onPress={handleAcceptAll}
        >
          <Text style={styles.acceptAllText}>Accept all</Text>
        </TouchableOpacity>

        {/* Next Button */}
        <TouchableOpacity
          style={[
            styles.nextButton,
            (!termsChecked || !dataProcessingChecked) && styles.nextButtonDisabled
          ]}
          onPress={handleContinue}
          disabled={!termsChecked || !dataProcessingChecked}
        >
          <Text style={styles.nextButtonText}>Next</Text>
        </TouchableOpacity>

        {/* Partnership Text */}
        <Text style={styles.partnershipText}>
          in Partnership with Sanda Diagnostic Center
        </Text>
      </Animated.View>
    </SafeAreaView>
  );
};

export default Consent;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8E8E8',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backIcon: {
    fontSize: 20,
    color: '#2D5016',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  shieldContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  logoText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2D5016',
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#2D5016',
    textAlign: 'center',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 40,
    paddingHorizontal: 20,
  },
  checkboxSection: {
    width: '100%',
    marginBottom: 30,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#C5C5C5',
    borderRadius: 12,
    marginRight: 12,
    marginTop: 2,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  checkboxChecked: {
    backgroundColor: '#2D5016',
    borderColor: '#2D5016',
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    flex: 1,
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  acceptAllButton: {
    width: '100%',
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  acceptAllText: {
    fontSize: 16,
    color: '#2D5016',
    fontWeight: '500',
  },
  nextButton: {
    backgroundColor: '#2D5016',
    borderRadius: 25,
    paddingVertical: 16,
    alignItems: 'center',
    width: '100%',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  nextButtonDisabled: {
    backgroundColor: '#C5C5C5',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  partnershipText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
});
