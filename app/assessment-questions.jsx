import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  FlatList,
  Alert
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BottomNavigation from './components/BottomNavigation';

const AssessmentQuestions = () => {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [assessment, setAssessment] = useState(null);
  
  // Mock assessment data
  const assessmentData = {
    'dass': {
      title: 'DASS (Depression Anxiety Stress Scales)',
      description: 'Please read each statement and select the option that indicates how much the statement applied to you over the past week.',
      options: [
        { value: 0, label: 'Did not apply to me at all' },
        { value: 1, label: 'Applied to me to some degree' },
        { value: 2, label: 'Applied to me to a considerable degree' },
        { value: 3, label: 'Applied to me very much' }
      ],
      questions: [
        'I found it hard to wind down',
        'I was aware of dryness of my mouth',
        'I could not seem to experience any positive feeling at all',
        'I experienced breathing difficulty',
        'I found it difficult to work up the initiative to do things',
        'I tended to over-react to situations',
        'I experienced trembling (e.g., in the hands)',
        'I felt that I was using a lot of nervous energy',
        'I was worried about situations in which I might panic',
        'I felt that I had nothing to look forward to'
      ]
    },
    'pss': {
      title: 'PSS (Perceived Stress Scale)',
      description: 'For each question, please indicate how often you have felt or thought a certain way during the last month.',
      options: [
        { value: 0, label: 'Never' },
        { value: 1, label: 'Almost Never' },
        { value: 2, label: 'Sometimes' },
        { value: 3, label: 'Fairly Often' },
        { value: 4, label: 'Very Often' }
      ],
      questions: [
        'In the last month, how often have you been upset because of something that happened unexpectedly?',
        'In the last month, how often have you felt that you were unable to control the important things in your life?',
        'In the last month, how often have you felt nervous and stressed?',
        'In the last month, how often have you felt confident about your ability to handle your personal problems?',
        'In the last month, how often have you felt that things were going your way?'
      ]
    }
  };

  useEffect(() => {
    // Set the assessment based on the ID and load saved answers
    if (id && assessmentData[id]) {
      setAssessment(assessmentData[id]);
      
      // Load saved answers if they exist
      loadSavedAnswers();
    } else {
      // Handle invalid assessment ID
      Alert.alert('Error', 'Invalid assessment selected');
      router.back();
    }
  }, [id]);

  // Load saved answers from AsyncStorage
  const loadSavedAnswers = async () => {
    try {
      const savedAnswers = await AsyncStorage.getItem(`${id}_answers`);
      const savedQuestion = await AsyncStorage.getItem(`${id}_currentQuestion`);
      
      if (savedAnswers) {
        const parsedAnswers = JSON.parse(savedAnswers);
        setAnswers(parsedAnswers);
        
        // If we have a saved question position, use it
        if (savedQuestion) {
          const questionIndex = parseInt(savedQuestion, 10);
          setCurrentQuestion(questionIndex);
          
          // If all questions are answered, show option to view results
          if (questionIndex >= assessmentData[id].questions.length - 1 && 
              !parsedAnswers.includes(null)) {
            Alert.alert(
              "Assessment Complete",
              "You have already completed this assessment. Would you like to view your results or start over?",
              [
                {
                  text: "View Results",
                  onPress: () => {
                    const results = calculateResults(parsedAnswers);
                    router.push({
                      pathname: '/assessment-results',
                      params: {
                        id,
                        results: JSON.stringify(results)
                      }
                    });
                  }
                },
                {
                  text: "Start Over",
                  onPress: () => {
                    resetAssessment();
                  }
                }
              ]
            );
          }
        }
      } else {
        // Initialize answers array with nulls
        setAnswers(new Array(assessmentData[id].questions.length).fill(null));
      }
    } catch (error) {
      console.error('Error loading saved answers:', error);
      // Initialize answers array with nulls as fallback
      setAnswers(new Array(assessmentData[id].questions.length).fill(null));
    }
  };

  // Save current answers and question position
  const saveProgress = async (newAnswers, questionIndex) => {
    try {
      await AsyncStorage.setItem(`${id}_answers`, JSON.stringify(newAnswers));
      await AsyncStorage.setItem(`${id}_currentQuestion`, questionIndex.toString());
    } catch (error) {
      console.error('Error saving progress:', error);
      Alert.alert('Error', 'Failed to save your progress');
    }
  };

  // Reset the assessment
  const resetAssessment = async () => {
    try {
      await AsyncStorage.removeItem(`${id}_answers`);
      await AsyncStorage.removeItem(`${id}_currentQuestion`);
      setAnswers(new Array(assessmentData[id].questions.length).fill(null));
      setCurrentQuestion(0);
    } catch (error) {
      console.error('Error resetting assessment:', error);
    }
  };

  const handleAnswer = (value) => {
    const newAnswers = [...answers];
    newAnswers[currentQuestion] = value;
    setAnswers(newAnswers);
    
    // Save progress
    const nextQuestion = currentQuestion + 1;
    saveProgress(newAnswers, nextQuestion < assessment.questions.length ? nextQuestion : currentQuestion);
    
    // Move to next question or show results
    if (currentQuestion < assessment.questions.length - 1) {
      setCurrentQuestion(nextQuestion);
    } else {
      // Calculate results and navigate to results screen
      const results = calculateResults(newAnswers);
      router.push({
        pathname: '/assessment-results',
        params: {
          id,
          results: JSON.stringify(results)
        }
      });
    }
  };

  // Add a function to handle navigation between questions
  const navigateToQuestion = (index) => {
    if (index >= 0 && index < assessment.questions.length) {
      setCurrentQuestion(index);
      saveProgress(answers, index);
    }
  };

  const calculateResults = (answers) => {
    // This is a simplified calculation for demonstration
    // In a real app, you would use proper scoring algorithms for each assessment
    if (id === 'dass') {
      // DASS has three subscales: depression, anxiety, and stress
      // For this demo, we'll just calculate a simple sum and divide into categories
      const sum = answers.reduce((acc, val) => acc + val, 0);
      // Simplified scoring
      let depression = Math.floor(sum / 3);
      let anxiety = (Math.floor(sum / 3) + (Math.random() * 5)).toFixed(2);
      let stress = (Math.floor(sum / 3) - (Math.random() * 3)).toFixed(2);
      // Ensure values are positive
      depression = Math.max(0, depression);
      anxiety = Math.max(0, anxiety);
      stress = Math.max(0, stress);
      return {
        depression: {
          score: depression,
          level: getLevel(depression)
        },
        anxiety: {
          score: anxiety,
          level: getLevel(anxiety)
        },
        stress: {
          score: stress,
          level: getLevel(stress)
        }
      };
    } else if (id === 'pss') {
      // PSS is a single score
      const sum = answers.reduce((acc, val) => acc + val, 0);
      return {
        stress: {
          score: sum,
          level: getLevel(sum / 2) // Adjusted for demo
        }
      };
    }
    return {};
  };

  const getLevel = (score) => {
    if (score < 5) return 'Normal';
    if (score < 10) return 'Mild';
    if (score < 15) return 'Moderate';
    if (score < 20) return 'Severe';
    return 'Extreme';
  };

  if (!assessment) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Loading assessment...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{assessment.title}</Text>
      </View>
      
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              { width: `${(currentQuestion / assessment.questions.length) * 100}%` }
            ]}
          />
        </View>
        <Text style={styles.progressText}>
          Question {currentQuestion + 1} of {assessment.questions.length}
        </Text>
      </View>
      
      {/* Navigation buttons for moving between questions */}
      <View style={styles.navigationButtons}>
        <TouchableOpacity 
          style={[styles.navButton, currentQuestion === 0 && styles.disabledButton]} 
          onPress={() => navigateToQuestion(currentQuestion - 1)}
          disabled={currentQuestion === 0}
        >
          <Text style={styles.navButtonText}>Previous</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.navButton, (currentQuestion === assessment.questions.length - 1 || answers[currentQuestion] === null) && styles.disabledButton]} 
          onPress={() => navigateToQuestion(currentQuestion + 1)}
          disabled={currentQuestion === assessment.questions.length - 1 || answers[currentQuestion] === null}
        >
          <Text style={styles.navButtonText}>Next</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.questionContainer}>
        <Text style={styles.questionText}>
          {assessment.questions[currentQuestion]}
        </Text>
      </View>
      
      <View style={styles.optionsContainer}>
        {assessment.options.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.optionButton,
              answers[currentQuestion] === option.value && styles.selectedOption
            ]}
            onPress={() => handleAnswer(option.value)}
          >
            <Text style={styles.optionText}>{option.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
      
      <Text style={styles.instructionText}>
        Answer each question honestly based on how you've been feeling over the past week.
        Your progress is automatically saved.
      </Text>
      
      <BottomNavigation />
    </SafeAreaView>
  );
};

export default AssessmentQuestions;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#6B9142',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 30,
  },
  progressContainer: {
    padding: 20,
  },
  progressBar: {
    height: 10,
    backgroundColor: '#F5F9EE',
    borderRadius: 5,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#6B9142',
    borderRadius: 5,
  },
  progressText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  navButton: {
    backgroundColor: '#6B9142',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 8,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },
  navButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  questionContainer: {
    padding: 25,
    backgroundColor: '#F5F9EE',
    margin: 20,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  questionText: {
    fontSize: 18,
    color: '#333333',
    textAlign: 'center',
    lineHeight: 26,
  },
  optionsContainer: {
    padding: 20,
  },
  optionButton: {
    backgroundColor: '#F5F9EE',
    borderRadius: 15,
    padding: 18,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedOption: {
    backgroundColor: '#E8F2D9',
    borderColor: '#6B9142',
    borderWidth: 2,
  },
  optionText: {
    fontSize: 16,
    color: '#444444',
    fontWeight: '500',
  },
  instructionText: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
    padding: 20,
    fontStyle: 'italic',
    marginBottom: 60, // Space for bottom navigation
  },
});