import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
  Image,
  Animated
} from 'react-native';
import { useRouter } from 'expo-router';
import { useUser } from './context/UserContext';

const EmailVerification = () => {
  const router = useRouter();
  const { userData, updateUserData } = useUser();
  const [code, setCode] = useState('');
  const [timer, setTimer] = useState(57);
  const [canResend, setCanResend] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  // Get email from user context
  const email = userData.email || "<EMAIL>";

  // Handle back button press - simplified to avoid BackHandler errors
  useEffect(() => {
    // We've removed the BackHandler code to fix the error

    // Instead, we'll just add a simple back button in the UI
    // that navigates to the create account screen
  }, [router]);

  // Check if user is already verified
  useEffect(() => {
    console.log("Email verification check - User data:", JSON.stringify(userData));

    // First check: if we have an existing user with emailVerified and hasCompletedProfile
    if (userData.emailVerified && userData.hasCompletedProfile) {
      console.log("User is verified and has completed profile, redirecting to dashboard");
      router.replace('/dashboard');
      return;
    }

    // Second check: if user is verified but needs to complete profile
    if (userData.emailVerified && userData.firstName) {
      console.log("User is verified and has personal info, redirecting to dashboard");
      router.replace('/dashboard');
      return;
    }

    // Third check: if user is verified but has no personal info
    if (userData.emailVerified && !userData.firstName) {
      console.log("User is verified but needs to complete personal info");
      router.replace('/personal-information');
      return;
    }

    // If we get here, user is not verified, show verification screen
    console.log("User is not verified, showing verification screen");
    setIsLoading(false);

    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, [userData, router]);

  useEffect(() => {
    let interval;
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer(prevTimer => prevTimer - 1);
      }, 1000);
    } else {
      setCanResend(true);
    }

    return () => clearInterval(interval);
  }, [timer]);

  const handleResendCode = () => {
    if (canResend) {
      // Logic to resend code would go here
      setTimer(60);
      setCanResend(false);
      Alert.alert("Code Resent", "A new verification code has been sent to your email.");
    }
  };

  const handleVerify = async () => {
    if (code.length === 6) {
      setIsVerifying(true);

      try {
        // Verify with Supabase OTP system only
        const otpVerifyResponse = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/auth/v1/verify`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
          },
          body: JSON.stringify({
            type: 'email',
            email: email,
            token: code
          }),
        });

        if (!otpVerifyResponse.ok) {
          // Code doesn't match
          Alert.alert("Invalid Code", "The verification code you entered is incorrect. Please try again.");
          setIsVerifying(false);
          return;
        }

        console.log('✅ Supabase OTP verification successful');

        // Update our custom user table
        const updateResponse = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/rest/v1/user?email=eq.${email}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'apikey': process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
            'Prefer': 'return=representation'
          },
          body: JSON.stringify({
            email_verified: true
          }),
        });

        if (!updateResponse.ok) {
          throw new Error('Failed to update verification status');
        }

        // Update user context to mark email as verified
        updateUserData({
          email: email,
          emailVerified: true
        });

        Alert.alert(
          "Email Verified!",
          "Your email has been successfully verified.",
          [
            {
              text: "Continue",
              onPress: () => router.push('/personal-information')
            }
          ]
        );

        setIsVerifying(false);
      } catch (error) {
        console.error('Verification error:', error);
        Alert.alert("Verification Failed", "There was an error verifying your code. Please try again.");
        setIsVerifying(false);
      }
    } else {
      Alert.alert("Invalid Code", "Please enter a valid 6-digit code.");
    }
  };

  // Show loading indicator while checking verification status
  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color="#6B9142" />
        <Text style={styles.loadingText}>Checking verification status...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Back Button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Logo */}
            <View style={styles.logoContainer}>
              <Image
                source={require('../assets/main.jpg')}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.logoText}>mentalease</Text>
            </View>

            {/* Title */}
            <Text style={styles.title}>Verify Your Email</Text>

            {/* Description */}
            <Text style={styles.description}>
              We've sent a verification code to you{'\n'}
              {email}
            </Text>

            {/* Input Label */}
            <Text style={styles.inputLabel}>Enter verification code</Text>

            {/* Code Input */}
            <TextInput
              style={styles.input}
              placeholder="Enter 6-digit code"
              placeholderTextColor="#999999"
              value={code}
              onChangeText={setCode}
              keyboardType="number-pad"
              maxLength={6}
              returnKeyType="done"
              onSubmitEditing={Keyboard.dismiss}
            />

            {/* Resend Section */}
            <View style={styles.resendSection}>
              <Text style={styles.resendText}>
                Didn't receive the code?
              </Text>
              <TouchableOpacity
                onPress={handleResendCode}
                disabled={!canResend}
              >
                <Text style={[styles.resendTimer, canResend && styles.resendActive]}>
                  {canResend ? 'Resend code' : `Resend code in ${timer} seconds`}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Verify Button */}
            <TouchableOpacity
              style={[
                styles.verifyButton,
                (code.length !== 6 || isVerifying) && styles.verifyButtonDisabled
              ]}
              onPress={() => {
                Keyboard.dismiss();
                handleVerify();
              }}
              disabled={code.length !== 6 || isVerifying}
            >
              <Text style={styles.verifyButtonText}>
                {isVerifying ? "Verifying..." : "Verify Email"}
              </Text>
            </TouchableOpacity>

            {/* Partnership Text */}
            <Text style={styles.partnershipText}>
              in Partnership with Sanda Diagnostic Center
            </Text>
          </Animated.View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EmailVerification;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8E8E8',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#2D5016',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backIcon: {
    fontSize: 20,
    color: '#2D5016',
    fontWeight: 'bold',
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 12,
  },
  logoText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2D5016',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2D5016',
    textAlign: 'center',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 40,
  },
  inputLabel: {
    fontSize: 16,
    color: '#2D5016',
    alignSelf: 'flex-start',
    marginBottom: 12,
    width: '100%',
  },
  input: {
    backgroundColor: '#C5C5C5',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginBottom: 20,
    fontSize: 16,
    width: '100%',
    textAlign: 'center',
    letterSpacing: 2,
    color: '#333333',
  },
  resendSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  resendText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  resendTimer: {
    fontSize: 14,
    color: '#999999',
  },
  resendActive: {
    color: '#2D5016',
    fontWeight: '600',
  },
  verifyButton: {
    backgroundColor: '#2D5016',
    borderRadius: 25,
    paddingVertical: 16,
    alignItems: 'center',
    width: '100%',
    marginBottom: 40,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  verifyButtonDisabled: {
    backgroundColor: '#C5C5C5',
  },
  verifyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  partnershipText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    marginTop: 20,
  },
});
