import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image,
  Animated
} from 'react-native';
import { useRouter } from 'expo-router';
import { useUser } from './context/UserContext';
import { signIn } from './lib/auth-service';

const SignIn = () => {
  const router = useRouter();
  const { updateUserData } = useUser();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleSignIn = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setIsLoading(true);

    try {
      console.log('Attempting to sign in with Supabase...');

      // Use our Supabase auth service
      const { data, error } = await signIn(email, password);

      if (error) {
        throw error;
      }

      console.log('Sign in successful:', data);

      // Get the user data from the database response
      const user = data.user;

      // Check if user has personal information but has_completed_profile is false
      const hasPersonalInfo = user.first_name && user.last_name && (user.age !== null && user.age !== undefined) && user.gender && user.phone && user.address;
      const shouldUpdateProfileFlag = hasPersonalInfo && !user.has_completed_profile;

      console.log('🔍 Profile check:', {
        first_name: !!user.first_name,
        last_name: !!user.last_name,
        age: user.age,
        age_valid: (user.age !== null && user.age !== undefined),
        gender: !!user.gender,
        phone: !!user.phone,
        address: !!user.address,
        hasPersonalInfo,
        current_flag: user.has_completed_profile,
        shouldUpdate: shouldUpdateProfileFlag
      });

      if (shouldUpdateProfileFlag) {
        console.log('🔄 User has personal info but profile flag is false. Updating database...');

        try {
          // Update the has_completed_profile flag in the database
          const updateResponse = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/rest/v1/user?email=eq.${user.email}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'apikey': process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
              'Prefer': 'return=representation'
            },
            body: JSON.stringify({
              has_completed_profile: true
            }),
          });

          if (updateResponse.ok) {
            console.log('✅ Profile completion flag updated in database');
            user.has_completed_profile = true; // Update local user object
          } else {
            console.log('❌ Failed to update profile completion flag');
          }
        } catch (error) {
          console.error('Error updating profile flag:', error);
        }
      }

      // Update user context with actual database values
      updateUserData({
        id: user.id || 'user_' + Math.random().toString(36).substring(2),
        email: user.email || email,
        emailVerified: user.email_verified || false,
        hasCompletedProfile: user.has_completed_profile || false,
        // Include any existing personal information
        firstName: user.first_name || "",
        middleName: user.middle_name || "",
        lastName: user.last_name || "",
        age: user.age || 0,
        gender: user.gender || "",
        civilStatus: user.civil_status || "",
        birthdate: user.birthdate || "",
        birthplace: user.birthplace || "",
        religion: user.religion || "",
        address: user.address || "",
        phone: user.phone || "",
        controlNumber: user.control_number || ""
      }, () => {
        console.log("User signed in successfully");

        // Let the dashboard handle the routing based on profile completion
        router.replace('/dashboard');
      });
    } catch (error) {
      console.error('Error signing in:', error.message);
      Alert.alert("Sign In Failed", error.message || "There was an error signing in. Please check your credentials and try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Back Button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Logo */}
            <View style={styles.logoContainer}>
              <Image
                source={require('../assets/main.jpg')}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.logoText}>mentalease</Text>
            </View>

            {/* Title */}
            <Text style={styles.title}>Log in to your account</Text>

            {/* Form */}
            <View style={styles.formContainer}>
              <TextInput
                style={styles.input}
                placeholder="Email Address"
                placeholderTextColor="#999999"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                returnKeyType="next"
                autoCorrect={false}
                textContentType="emailAddress"
              />

              <TextInput
                style={styles.input}
                placeholder="Password"
                placeholderTextColor="#999999"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                returnKeyType="done"
                onSubmitEditing={Keyboard.dismiss}
                autoCorrect={false}
                textContentType="password"
              />

              <TouchableOpacity
                style={[styles.continueButton, isLoading && styles.continueButtonDisabled]}
                onPress={() => {
                  Keyboard.dismiss();
                  handleSignIn();
                }}
                disabled={isLoading}
              >
                <Text style={styles.continueButtonText}>
                  {isLoading ? "Signing in..." : "Continue"}
                </Text>
              </TouchableOpacity>

              {/* Or divider */}
              <Text style={styles.orText}>Or</Text>

              {/* Sign Up Link */}
              <TouchableOpacity
                style={styles.signUpButton}
                onPress={() => router.push('/create-account')}
              >
                <Text style={styles.signUpText}>Sign Up</Text>
              </TouchableOpacity>
            </View>

            {/* Partnership Text */}
            <Text style={styles.partnershipText}>
              in Partnership with Sanda Diagnostic Center
            </Text>
          </Animated.View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default SignIn;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8E8E8',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backIcon: {
    fontSize: 20,
    color: '#2D5016',
    fontWeight: 'bold',
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 12,
  },
  logoText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2D5016',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2D5016',
    textAlign: 'center',
    marginBottom: 40,
  },
  formContainer: {
    width: '100%',
    maxWidth: 350,
    marginBottom: 30,
  },
  input: {
    backgroundColor: '#C5C5C5',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginBottom: 16,
    fontSize: 16,
    color: '#333333',
  },
  continueButton: {
    backgroundColor: '#2D5016',
    borderRadius: 25,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  continueButtonDisabled: {
    backgroundColor: '#A9A9A9',
    opacity: 0.7,
  },
  orText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
  },
  signUpButton: {
    alignItems: 'center',
  },
  signUpText: {
    fontSize: 16,
    color: '#2D5016',
    fontWeight: '600',
  },
  partnershipText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    marginTop: 20,
  },
});




