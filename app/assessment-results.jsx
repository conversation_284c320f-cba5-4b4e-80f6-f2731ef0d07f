import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Platform,
  ActivityIndicator,
  Share,
  Alert
} from 'react-native';
import { useState, useEffect, useCallback } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BottomNavigation from './components/BottomNavigation';

const AssessmentResults = () => {
  const router = useRouter();
  const { id, results: resultsParam } = useLocalSearchParams();
  const [results, setResults] = useState(null);
  const [assessmentTitle, setAssessmentTitle] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaved, setIsSaved] = useState(false);
  const [savedDate, setSavedDate] = useState(null);
  
  // Share results function
  const shareResults = useCallback(async () => {
    try {
      if (!results) return;
      // Format results for sharing
      let message = `My ${assessmentTitle} Results:\n\n`;
      Object.entries(results).forEach(([category, data]) => {
        message += `${category.charAt(0).toUpperCase() + category.slice(1)}: ${data.score} - ${data.level}\n`;
      });
      message += "\nTaken via MentalEase App";
      await Share.share({
        message,
        title: `${assessmentTitle} Results`,
      });
    } catch (error) {
      console.error('Error sharing results:', error);
    }
  }, [results, assessmentTitle]);

  // Check if this assessment result is already saved
  const checkIfSaved = useCallback(async () => {
    try {
      const savedResults = await AsyncStorage.getItem(`${id}_saved_results`);
      const timestamp = await AsyncStorage.getItem(`${id}_result_timestamp`);
      
      if (savedResults) {
        setIsSaved(true);
        if (timestamp) {
          setSavedDate(new Date(timestamp));
        }
      }
    } catch (error) {
      console.error('Error checking saved status:', error);
    }
  }, [id]);

  // Save assessment results
  const saveAssessment = useCallback(async () => {
    try {
      if (!results) return;
      
      // Save the results
      await AsyncStorage.setItem(`${id}_saved_results`, JSON.stringify(results));
      
      // Save the timestamp
      const timestamp = new Date().toISOString();
      await AsyncStorage.setItem(`${id}_result_timestamp`, timestamp);
      setSavedDate(new Date(timestamp));
      
      // Mark current assessment as complete
      await AsyncStorage.removeItem(`${id}_answers`);
      await AsyncStorage.removeItem(`${id}_currentQuestion`);
      
      setIsSaved(true);
      Alert.alert(
        "Success",
        "Your assessment results have been saved successfully!",
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error('Error saving assessment:', error);
      Alert.alert(
        "Error",
        "Failed to save your assessment results. Please try again.",
        [{ text: "OK" }]
      );
    }
  }, [results, id]);

  useEffect(() => {
    setIsLoading(true);
    if (resultsParam) {
      try {
        const parsedResults = JSON.parse(resultsParam);
        setResults(parsedResults);
      } catch (error) {
        console.error('Error parsing results:', error);
      }
    }
    
    // Set assessment title based on ID
    if (id === 'dass') {
      setAssessmentTitle('DASS (Depression Anxiety Stress Scales)');
    } else if (id === 'pss') {
      setAssessmentTitle('PSS (Perceived Stress Scale)');
    } else if (id === 'gad7') {
      setAssessmentTitle('GAD-7 (Generalized Anxiety Disorder)');
    } else if (id === 'phq9') {
      setAssessmentTitle('PHQ-9 (Patient Health Questionnaire)');
    }
    
    // Check if this assessment result is already saved
    checkIfSaved();
    
    setIsLoading(false);
  }, [resultsParam, id, checkIfSaved]);

  const getLevelColor = (level) => {
    switch (level) {
      case 'Normal':
        return '#6B9142'; // Green
      case 'Mild':
        return '#A3C677'; // Light green
      case 'Moderate':
        return '#FFC107'; // Yellow
      case 'Severe':
        return '#FF9800'; // Orange
      case 'Extreme':
        return '#F44336'; // Red
      default:
        return '#6B9142';
    }
  };

  const getRecommendations = (category, level) => {
    const recommendations = {
      depression: {
        Normal: [
          'Continue with your current healthy habits',
          'Maintain regular exercise and social connections',
          'Practice gratitude daily'
        ],
        Mild: [
          'Consider adding more physical activity to your routine',
          'Establish a regular sleep schedule',
          'Connect with friends and family regularly'
        ],
        Moderate: [
          'Consider speaking with a mental health professional',
          'Practice mindfulness meditation daily',
          'Establish a routine that includes enjoyable activities'
        ],
        Severe: [
          'Consult with a mental health professional',
          'Consider joining a support group',
          'Establish a self-care routine and stick to it'
        ],
        Extreme: [
          'Seek professional help immediately',
          'Contact a crisis helpline if needed',
          'Reach out to trusted friends or family for support'
        ]
      },
      anxiety: {
        Normal: [
          'Continue with your current healthy habits',
          'Practice regular relaxation techniques',
          'Maintain a balanced lifestyle'
        ],
        Mild: [
          'Try deep breathing exercises when feeling anxious',
          'Limit caffeine and alcohol consumption',
          'Practice progressive muscle relaxation'
        ],
        Moderate: [
          'Consider speaking with a mental health professional',
          'Practice daily mindfulness meditation',
          'Identify and challenge anxious thoughts'
        ],
        Severe: [
          'Consult with a mental health professional',
          'Learn and practice grounding techniques',
          'Establish a regular exercise routine'
        ],
        Extreme: [
          'Seek professional help immediately',
          'Contact a crisis helpline if needed',
          'Practice self-compassion and acceptance'
        ]
      },
      stress: {
        Normal: [
          'Continue with your current healthy habits',
          'Practice regular relaxation techniques',
          'Maintain work-life balance'
        ],
        Mild: [
          'Incorporate stress-reduction activities into your routine',
          'Practice time management techniques',
          'Ensure adequate sleep and nutrition'
        ],
        Moderate: [
          'Consider speaking with a mental health professional',
          'Practice daily stress management techniques',
          'Identify and address sources of stress'
        ],
        Severe: [
          'Consult with a mental health professional',
          'Prioritize self-care and stress reduction',
          'Consider lifestyle changes to reduce stress'
        ],
        Extreme: [
          'Seek professional help immediately',
          'Take immediate steps to reduce stressors',
          'Practice self-compassion and acceptance'
        ]
      }
    };
    return recommendations[category]?.[level] || [];
  };

  const formatDate = (date) => {
    if (!date) return '';
    return date.toLocaleDateString() + ' at ' + 
           date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (isLoading || !results) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar
          translucent={true}
          backgroundColor="transparent"
          barStyle="dark-content"
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6B9142" />
          <Text style={styles.loadingText}>Loading your results...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        translucent={true}
        backgroundColor="transparent"
        barStyle="dark-content"
      />
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.push('/dashboard')} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Home</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Your Assessment Results</Text>
        <TouchableOpacity onPress={shareResults} style={styles.shareButton}>
          <Text style={styles.shareButtonText}>Share</Text>
        </TouchableOpacity>
      </View>
      <ScrollView contentContainerStyle={styles.content}>
        <Text style={styles.assessmentTitle}>{assessmentTitle}</Text>
        <Text style={styles.subtitle}>
          Answer each question honestly based on how you've been feeling over the past week
        </Text>
        
        {/* Display saved status and date if saved */}
        {isSaved && (
          <View style={styles.savedBadge}>
            <Text style={styles.savedText}>✓ Saved</Text>
            {savedDate && (
              <Text style={styles.savedDateText}>
                {formatDate(savedDate)}
              </Text>
            )}
          </View>
        )}
        
        {Object.entries(results).map(([category, data]) => (
          <View key={category} style={styles.resultCard}>
            <View style={styles.resultHeader}>
              <Text style={styles.categoryTitle}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Text>
              <View style={styles.scoreContainer}>
                <Text style={styles.scoreValue}>{data.score}</Text>
                <Text
                  style={[
                    styles.levelText,
                    { color: getLevelColor(data.level) }
                  ]}
                >
                  {data.level}
                </Text>
              </View>
            </View>
            {getRecommendations(category, data.level).length > 0 && (
              <View style={styles.recommendationsContainer}>
                <Text style={styles.recommendationsTitle}>Recommendations:</Text>
                {getRecommendations(category, data.level).map((rec, index) => (
                  <Text key={index} style={styles.recommendationItem}>• {rec}</Text>
                ))}
              </View>
            )}
          </View>
        ))}
        
        <View style={styles.disclaimerContainer}>
          <Text style={styles.disclaimerText}>
            Disclaimer: This assessment is for self-evaluation purposes only and should not be considered a professional diagnosis. Please consult with a qualified healthcare professional for proper evaluation and treatment.
          </Text>
        </View>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.saveButton, isSaved && styles.disabledButton]}
            onPress={saveAssessment}
            activeOpacity={0.8}
            disabled={isSaved}
          >
            <Text style={styles.saveButtonText}>
              {isSaved ? 'Assessment Saved' : 'Save Assessment'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.shareResultsButton}
            onPress={shareResults}
            activeOpacity={0.8}
          >
            <Text style={styles.shareResultsButtonText}>Share Results</Text>
          </TouchableOpacity>
        </View>
        
        {/* Add a button to take new assessment if already saved */}
        {isSaved && (
          <TouchableOpacity
            style={styles.newAssessmentButton}
            onPress={() => router.push(`/assessment-questions?id=${id}`)}
            activeOpacity={0.8}
          >
            <Text style={styles.newAssessmentButtonText}>Take New Assessment</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
      <BottomNavigation />
    </SafeAreaView>
  );
};

export default AssessmentResults;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 8,
    width: 60,
  },
  backButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    textAlign: 'center',
    color: '#6B9142',
    fontSize: 18,
    fontWeight: 'bold',
  },
  shareButton: {
    padding: 8,
    width: 60,
    alignItems: 'flex-end',
  },
  shareButtonText: {
    color: '#6B9142',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    padding: 20,
    paddingBottom: 80, // Space for bottom navigation
  },
  assessmentTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 10,
    textAlign: 'center',
    paddingHorizontal: 10,
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 25,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 15,
  },
  savedBadge: {
    alignSelf: 'center',
    backgroundColor: '#E8F2D9',
    paddingVertical: 5,
    paddingHorizontal: 12,
    borderRadius: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#6B9142',
  },
  savedText: {
    color: '#6B9142',
    fontWeight: 'bold',
    fontSize: 14,
    textAlign: 'center',
  },
  savedDateText: {
    color: '#6B9142',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 2,
  },
  resultCard: {
    backgroundColor: '#F5F9EE',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  scoreContainer: {
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 10,
    minWidth: 70,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  scoreText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  interpretationText: {
    fontSize: 14,
    color: '#6B9142',      
  },
  shareResultsButton: {
    backgroundColor: '#6B9142',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 20,
  },
  shareResultsText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },  
  instructionText: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
    padding: 20,
    fontStyle: 'italic',
    marginBottom: 60, // Space for bottom navigation
  },
});