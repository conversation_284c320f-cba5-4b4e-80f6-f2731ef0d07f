{"name": "capstone", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@supabase/supabase-js": "^2.49.7", "@tradle/react-native-http": "^2.0.1", "buffer": "^6.0.3", "capstone": "file:", "cors": "^2.8.5", "crypto-browserify": "^3.12.1", "events": "^3.3.0", "expo": "~53.0.9", "expo-constants": "~17.1.6", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.4", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "express": "^5.1.0", "https-browserify": "^1.0.0", "nodemailer": "^7.0.3", "openai": "^5.0.1", "os-browserify": "^0.3.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-get-random-values": "^1.11.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-tcp-socket": "^6.3.0", "react-native-url-polyfill": "^2.0.0", "stream-browserify": "^3.0.0", "url": "^0.11.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}